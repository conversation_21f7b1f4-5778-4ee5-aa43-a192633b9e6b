{"Intentions": [{"intentionName": "undefined", "description": "A user asks for something completely unrelated to qualitative analysis, project work, business consulting, or the current conversation context. This should only be used for clearly off-topic requests.", "userMessageExamples": ["How can I add a user to my ClickUp account?", "What is the best basketball player in US?", "I really need to update my instagram profile image, can you help?", "Can you help me with my math homework?", "What's the weather like today?", "How do I cook pasta?"]}, {"intentionName": "generate_qual", "description": "The user asks to create or generate a qualitative analysis (qual) based on data, project or other information. All data is provided in the prompt.", "userMessageExamples": ["Please generate a qual based on this project", "Can you create a qual for these survey results?"]}, {"intentionName": "extraction", "description": "User provides a client name, member firm, objectives or results, country, people involved, or anything that can describe a project. This is for initial data provision, not for changing already confirmed information.", "userMessageExamples": ["Microsoft", "Google", "Apple", "Amazon", "Netflix", "Engagement locations are Ukraine, Taiwan, Brazil", "Engagement location Peru", "20.06.2025 to 21.03.2026 we expect an engagement that will focus on providing measurable impact by delivering a digital transformation strategy. The goal is to enhance operationalefficiency", "Client name is CoolTech US", "Our sales increased by 15% in April 2023.", "We're helping Acme Inc. with their digital", "Well the goals were much higher than the reality we've got", "This time we focused on Canadian department, instead of Taiwanese", "Write up a qual for my recent project. We advised the client on how to develop and implement automation technology.", "Use this case study deck to write a qual for an engagement where we assisted the client with a large business process outsourcing program for an Automotive Captive.", "Attached is a proposal where Deloitte provided a strategic vision for a health care faciltity. Use this to start a qual.", "Based on this value delivered write-up, create a new qual for my client, Mercedes Benz. Deloitte explored and did end-to-end pilot for testing automation for 12 scenarios and 200 steps with 10 sub-processes.We supported the client for actively driving forward the transition from automotive manufacturer to the networked mobility service provider. They changed their business model from wholesale of the vehicle to dealer and then dealer to final customer to directly selling their products to the end customers. Benefits: this digital transformation enabled them to have full control on customer journey instead of relying on dealers and franchise partners thus minimizing discrepancies in pricing, stock, data, and CRM. The role of the dealerships was transformed from retailers to facilitators thus ensuring strict adherence to process and controls and minimizing process variables by ensuring use of their globally approved system components. The control test automation enabled the client to reduce time spent on manually testing applications and error prone manual data migration during rollout thus ensuring a faster and seamless transition", "Expand on the business issue described below. SAMA created a special purpose entity to manage and operate the payment systems (wholesale and retail) under its ambit. In order to ensure a smooth process and workforce transition and efficient operations of the newly created entity, SAMA (Saudi Payments) required support in designing the most critical layers of the new target operating model. Check tense and grammar", "Create a qual for the client google", "The project started in March and ended in June", "We worked with Microsoft on a digital transformation project", "The engagement was in Canada and focused on supply chain optimization"]}, {"intentionName": "example", "description": "A user asks for an example, a template or a demonstration of something. This can be a direct request to show somthing, or a question about what datapoints to include", "userMessageExamples": ["Show me an example prompt", "Can you give me an example prompt?", "Show me a template for data analysis", "I don't get how should I approach this task. Can you give me an example of these fields filled in with come real project info?"]}, {"intentionName": "dash_discard", "description": "The user wants to reject, ignore or discard certain information on dashboard(selecting dash task) and instead wants to provide data in prompts.", "userMessageExamples": ["Discard this analysis please", "I dont need this section, remove it", "No, skip tasks", "No, create new qual", "No, I don't want to use dash tasks", "No, skip the dash tasks", "No, create new qual without dash task", "I don't want to use this dash task information", "Skip this dash task and let me provide my own data", "Discard the dash task details", "No, I want to start fresh without dash tasks", "Remove this dash task information"]}, {"intentionName": "uncertainty", "description": "A user expresses uncertainty about what to do next. This can be a request for help, a question about the next steps, or a general inquiry about the process.", "userMessageExamples": ["I'm new here, what should I write?", "Not sure what to do", "How do I create a qual?", "What information do you need?", "What do i do next? Can I use this bot as my personal assistant, for example?", "Are you capable of creating my report?", "Can you even act as a report generator?"]}, {"intentionName": "need_context", "description": "A user wants to work with some data. It can be a request for a description, request to generate Qual or a request to provide any information about the project. As the user did not provide any information about the project, this request will need more context.", "userMessageExamples": ["Write a brief description of my project", "Please generate a qual based on this project", "Could you provide a brief summary of what i've done?", "What client is my report about?"]}, {"intentionName": "user_confirmation", "description": "A user confirms somthing, such as a client name", "userMessageExamples": ["Yes, the client is Deloitte US", "That's correct, we are helping Acme Inc. with their digital transformation", "Yes", "Yep", "Correct", "Fine", "Ok"]}, {"intentionName": "user_denial", "description": "A user does not agree with an information provided. It can be a short message with a single word, or a longer message with a request to change something. User can provide a different information on the contrary with, but if the main point is to deny something, then it is a duser_denial intention.", "userMessageExamples": ["No", "Actually it is Deloitte UK", "Not really, it is the name of their client, but the parent company in Architects Inc.", "I wouldn't say so", "I'll enter client name myself", "I'll enter LDMF", "I'll enter Lead Deloitte Member Firm"]}, {"intentionName": "change_engagement_dates", "description": "A user wants to change engagement dates or provides new engagement dates to replace existing ones. This includes explicit requests to change dates as well as providing new date information that should replace current dates. At the same time the user does not provide any other information about the project, such as client name, country, people involved, etc.", "userMessageExamples": ["I need to change the project start date.", "Can we reschedule the engagement for next month?", "The client wants to push back the deadline to Friday.", "Could you update the end date of this project?", "We need to adjust the timeline for this engagement.", "I'd like to change the dates for this report.", "The project dates need to be modified.", "Can we extend the engagement period?", "I want to change when this engagement is scheduled to end.", "The engagement dates are March 15 to May 20", "Project runs from January 10 to March 15", "We worked from April 1st to June 30th", "The dates should be Q2 2024", "Update the timeline to 2024-03-15 to 2024-05-20", "Actually, the engagement was from March 1 to April 30", "Change it to March 15 - May 20", "The correct dates are January 10 - March 15", "The project dates are March 15 to May 20", "We worked from January 10 to March 15", "The engagement period was April 1st to June 30th", "Update the dates to Q2 2024", "The timeline should be March 15 - May 20"]}, {"intentionName": "manual_ldmf_input", "description": "A user provides a Lead Deloitte Member Firm (LDMF) country or location manually. It must be a single country name, with no additional information", "userMessageExamples": ["Germany", "United States", "Canada", "Australia", "UK", "Singapore", "Italy", "France", "Japan", "Brazil", "India", "South Africa", "Netherlands", "Switzerland", "Spain", "United Kingdom", "New Zealand", "Mexico", "Argentina", "Chile"]}, {"intentionName": "navigate_to_resource_page", "description": "A user requests information related to client engagements, qualifications, or professional expertise", "userMessageExamples": ["Can you provide top Deloitte's procurement quals?", "Can I get Quals related to digitization in Africa?", "Give me details about Citigroup client", "Fetch me quals where <PERSON><PERSON><PERSON> is the client", "I'm working with a client named VMware, Inc. and want to know more about the company. Where can I find more information?", "Help me find professionals with experience in data migration and are from US member firm"]}]}