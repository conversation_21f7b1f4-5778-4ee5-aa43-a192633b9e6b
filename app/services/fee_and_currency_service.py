import logging

from cachetools import TTL<PERSON>ache

from repositories.service import ServiceRepository
from schemas.service import <PERSON><PERSON><PERSON>cy<PERSON><PERSON>, ProjectFeeDisplayItem


__all__ = ['FeeAndCurrencyService']

logger = logging.getLogger(__name__)


class FeeAndCurrencyService:
    """Service for fee and currency operations."""

    _cache: TTLCache = TTLCache(maxsize=1, ttl=60 * 60 * 1)  # Cache for 1 hour

    def __init__(self, service_repository: ServiceRepository):
        self.service_repository = service_repository

    async def get_project_fee_display_options(self, token: str) -> list[ProjectFeeDisplayItem]:
        """
        List all project fee display options.

        Returns:
            List of project fee display options.

        Raises:
            Exception: If an error occurs while listing project fee display options.
        """
        try:
            logger.debug('Getting cached project fee display options')
            cache_key = 'fee_display_options'
            fee_display_options = self._cache.get(cache_key)
            if fee_display_options is not None:
                logger.debug('Returning project fee display options from cache')
                return fee_display_options

            logger.info('Listing project fee display options')
            options = await self.service_repository.get_project_fee_display_options(token)
            self._cache[cache_key] = options

            return self._cache[cache_key]
        except Exception as e:
            logger.error('Error listing project fee display options: %s', e)
            raise

    async def get_currencies(self, token: str) -> list[CurrencyItem]:
        """
        List all currencies.

        Returns:
            List of currencies.

        Raises:
            Exception: If an error occurs while listing currencies.
        """
        try:
            logger.debug('Getting cached currencies')
            cache_key = 'currencies'
            currencies = self._cache.get(cache_key)
            if currencies is not None:
                logger.debug('Returning currencies from cache')
                return currencies

            logger.info('Listing currencies')
            currencies = await self.service_repository.get_currencies(token)
            self._cache[cache_key] = currencies

            return self._cache[cache_key]
        except Exception as e:
            logger.error('Error listing currencies: %s', e)
            raise
