import datetime
import logging
from typing import List, Sequence, cast
from uuid import UUID

from fastapi import UploadFile

from config import settings
from constants.durable_functions import ProcessingStatus
from constants.extracted_data import (
    ConversationState,
    MissingDataStatus,
    RequiredField,
)
from constants.message import (
    ALL_REQUIRED_FIELDS_EXTRACTED_CLIENT_NOT_FOUND,
    ALL_REQUIRED_FIELDS_EXTRACTED_DOCS,
    THANX_FOR_INFORMATION,
    ConversationMessageIntention,
    CorruptedUtilsTemplate,
    MessageRole,
    MessageType,
    OptionType,
    SuggestedUserPrompt,
    SystemReplyType,
)
from constants.prompt import prompt_templates
from core.utils import truncate_filename
from exceptions import ConfirmedDataReplacementError
from exceptions.conversation import ConversationDataInconsistencyError
from exceptions.entity import EntityNotFoundError
from models import QualConversation
from repositories import (
    ConversationMessageRepository,
    ConversationRepository,
    DocumentDbRepository,
    ProcessingMessageRepository,
)
from repositories.openai import OpenAIRepository
from schemas import (
    AggregatedData,
    BaseMessageSerializer,
    ClientNameOption,
    CombinedMessageSerializer,
    Command,
    ConfirmedData,
    ConversationData,
    ConversationMessageProcessingResult,
    DatePickerOption,
    DocumentCreationRequest,
    KXDashTaskOption,
    LDMFCountryOption,
    MessageContent,
    MessageValidator,
    Option,
    SystemMessageSerializer,
    UserMessageSerializer,
)
from schemas.extracted_data import FieldHandlerResponse
from services.client_industry import ClientIndustryDataService
from services.client_service import ClientServiceDataService
from services.date_validator import DateValidatorService
from services.document import DocumentService
from services.extracted_data import ExtractedDataService
from services.intent_classifier import IntentClassifierService
from services.kx_dash import KXDashService
from services.message_handlers.base import MessageHandler
from services.message_processor import ConversationMessageProcessor
from services.option_handlers import (
    ClientNameOptionHandlerService,
    DatesOptionHandlerService,
    KXDashTaskOptionHandlerService,
    LDMFCountryOptionHandlerService,
)
from services.proactive_chat import ProactiveChatService
from services.suggestions import SuggestedPromptsGenerator
from services.system_message_generation import SystemMessageGenerationService
from services.system_message_reply_type import SystemMessageReplyTypeGenerator
from services.translation import TranslationService


__all__ = ['PromptMessageHandler']

logger = logging.getLogger(__name__)


class PromptMessageHandler(MessageHandler):
    _EMPTY_LDMF_COUNTRY_OPTION = [LDMFCountryOption(type=OptionType.LDMF_COUNTRY, ldmf_country='')]

    def __init__(
        self,
        conversation_message_repository: ConversationMessageRepository,
        conversation_repository: ConversationRepository,
        document_service: DocumentService,
        document_db_repository: DocumentDbRepository,
        processing_message_repository: ProcessingMessageRepository,
        kx_dash_service: KXDashService,
        translation_service: TranslationService,
        extracted_data_service: ExtractedDataService,
        date_validator_service: DateValidatorService,
        system_message_generation_service: SystemMessageGenerationService,
        client_industry_service: ClientIndustryDataService,
        client_service_service: ClientServiceDataService,
        openai_repository: OpenAIRepository,
        # Option handlers
        client_name_option_handler: ClientNameOptionHandlerService,
        ldmf_country_option_handler: LDMFCountryOptionHandlerService,
        dates_option_handler: DatesOptionHandlerService,
        kx_dash_task_option_handler: KXDashTaskOptionHandlerService,
    ):
        self.conversation_message_repository = conversation_message_repository
        self.conversation_repository = conversation_repository
        self.document_service = document_service
        self.document_db_repository = document_db_repository
        self.processing_message_repository = processing_message_repository
        self.kx_dash_service = kx_dash_service
        self.translation_service = translation_service
        self.intent_classifier_service = IntentClassifierService(
            openai_service=openai_repository,
            system_prompt_template=prompt_templates.prompt_page_intention.SYSTEM,
            user_prompt_template=prompt_templates.prompt_page_intention.USER,
            intentions=prompt_templates.prompt_page_intention.GENERAL_INTENTIONS,
            intent_object_structure=prompt_templates.prompt_page_intention.INTENT_OBJECT_STRUCTURE,
        )
        self.extracted_data_service = extracted_data_service
        self.date_validator_service = date_validator_service
        self.system_message_service = system_message_generation_service
        self.client_industry_service = client_industry_service
        self.client_service_service = client_service_service
        # Option handlers
        self.client_name_option_handler = client_name_option_handler
        self.ldmf_country_option_handler = ldmf_country_option_handler
        self.dates_option_handler = dates_option_handler
        self.kx_dash_task_option_handler = kx_dash_task_option_handler

    async def handle(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        command: Command | None,  # argument is not used for purpose to keep the same signature as other handlers
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer:
        await self._ensure_previous_message_processed(conversation_id)

        content = content.strip()
        if files:
            message_type = MessageType.TEXT_WITH_FILE if content else MessageType.FILE
        else:
            message_type = MessageType.TEXT

        user_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=message_type,
            content=content,
            selected_option=selected_option,
            files=files,
            system_reply_type=None,
        )

        user_message = await self.conversation_message_repository.create(user_message_to_persist)
        user_message = cast(UserMessageSerializer, user_message)

        conversation_data = None
        intention = None
        system_reply_type = None

        if selected_option:
            try:
                match selected_option.type:
                    case OptionType.CLIENT_NAME:
                        system_message = await self.client_name_option_handler.handle(
                            selected_option, conversation_id, token
                        )
                    case OptionType.LDMF_COUNTRY:
                        system_message = await self.ldmf_country_option_handler.handle(
                            selected_option, conversation_id, token
                        )
                    case OptionType.DATES:
                        system_message = await self.dates_option_handler.handle(selected_option, conversation_id, token)
                    case OptionType.KX_DASH_TASK:
                        system_message = await self.kx_dash_task_option_handler.handle(
                            selected_option, conversation_id, token
                        )
                    case _:
                        raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
            except ConfirmedDataReplacementError:
                logger.info(
                    'ConfirmedDataReplacementError detected in %s for selected option %s',
                    'PromptMessageHandler.handle',
                    selected_option.type,
                )
                system_message = await self._handle_confirmed_fields_change_prohibited_message(conversation_id)
                return CombinedMessageSerializer(
                    user=user_message, system=cast(SystemMessageSerializer, system_message)
                )
        else:
            # Get conversation message history for message processor & suggested prompts tracking.
            conversation_data = await self._fetch_conversation_data(conversation_id, token)
            conversation_message_history = conversation_data.conversation_message_history

            # Process the user message to get the system reply and intention
            if content:
                translated_content = await self.translation_service.get_translated_text(content)
                if translated_content != user_message.content:
                    # If the message.content was translated, update message
                    await self.conversation_message_repository.update_fields(
                        user_message.id,
                        {
                            'Translation': translated_content,
                        },
                    )
                    user_message.content = translated_content
            message_processor = ConversationMessageProcessor(
                conversation_id=conversation_id,
                conversation_data=conversation_data,
                user_message=user_message,
                conversation_message_history=conversation_message_history,
                intent_classifier_service=self.intent_classifier_service,
                extracted_data_service=self.extracted_data_service,
                conversation_repository=self.conversation_repository,
                conversation_message_repository=self.conversation_message_repository,
                date_validator_service=self.date_validator_service,
                document_service=self.document_service,
                token=token,
            )
            try:
                message_processing_result: ConversationMessageProcessingResult = await message_processor.run()
            except ConfirmedDataReplacementError:
                logger.info(
                    'ConfirmedDataReplacementError detected in %s',
                    'ConversationMessageProcessor.run',
                )
                system_message = await self._handle_confirmed_fields_change_prohibited_message(conversation_id)
                return CombinedMessageSerializer(
                    user=user_message, system=cast(SystemMessageSerializer, system_message)
                )

            system_reply_type = message_processing_result.system_reply_type
            intention = message_processing_result.intention
            user_message_to_persist.intention = intention

            # Get aggregated extracted and confirmed data after message processing.
            conversation_data = await self._fetch_conversation_data(conversation_id, token)

            # Get suggested prompts
            suggested_prompts = await self.get_suggested_prompts(
                conversation_id=conversation_id,
                conversation_data=conversation_data,
                user_message=user_message,
                files=files,
                intention=intention,
                current_reply_type=system_reply_type,
                called_from='PromptMessageHandler.handle.no_options_flow',
            )

            # Update the user message with the determined intention
            await self.conversation_message_repository.update_fields(user_message.id, {'Intention': intention})

            dash_discard_intentions = {
                ConversationMessageIntention.DASH_DISCARD,
                ConversationMessageIntention.USER_DENIAL,
                ConversationMessageIntention.GENERATE_QUAL,
            }

            if intention in dash_discard_intentions and system_reply_type == SystemReplyType.WELCOME_MESSAGE:
                dash_discard_message = await self._get_dash_discard_response(
                    user_message, message_processing_result, suggested_prompts
                )
                return dash_discard_message

            system_message = None
            should_generate_system_message = not (
                (content and intention == ConversationMessageIntention.EXTRACTION) or files
            )
            if should_generate_system_message:
                system_message = await self._get_system_message_with_result(
                    user_message_to_persist,
                    message_processing_result,
                    suggested_prompts,
                    conversation_data.confirmed_data,
                )
                if not system_message.content:
                    system_message = None

        if not conversation_data:
            conversation_data = await self._fetch_conversation_data(conversation_id, token)

        # Append enriched message to system message
        if system_message:
            confirmed_data = conversation_data.confirmed_data
            is_dash_task = isinstance(selected_option, KXDashTaskOption)
            proactive_chat_service = ProactiveChatService(
                conversation_data=conversation_data,
                system_reply_type=system_message.system_reply_type,
                is_dash_task=is_dash_task,
            )

            if is_dash_task:
                await self.autoconfirm_data_from_kxdash(conversation_data, conversation_id, token)

            # Get proactive message content
            proactive_message_content, proactive_system_reply = proactive_chat_service.get_proactive_system_message()
            missing_data_response = None
            # Get proactive options
            if (
                proactive_message_content
                and not system_message.options
                and system_message.system_reply_type
                not in [
                    SystemReplyType.BRIEF_DESCRIPTION,
                    SystemReplyType.EXAMPLE,
                    SystemReplyType.CLIENT_NOT_FOUND,
                    SystemReplyType.CLIENT_NAME_TOO_LONG,
                    SystemReplyType.DASH_TASK_SELECTED_TEMPLATE,
                ]
            ):
                missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                    conversation_id=conversation_id,
                    token=token,
                    confirmed_data=confirmed_data,
                    called_from='PromptMessageHandler.handle',
                    conversation_data=conversation_data,
                )
                missing_eng_dates = missing_data_response.next_expected_field == RequiredField.ENGAGEMENT_DATES
                proactive_options = self._convert_to_option_objects(
                    missing_data_response.options, missing_data_response.conversation_state
                )
                changed_ldmf_option = self.extracted_data_service.catch_invalid_ldmf_option(
                    missing_data_response, missing_data_response.conversation_state, system_reply_type
                )
                status_is_missing = missing_data_response.status == MissingDataStatus.MISSING_DATA
                is_proactive_options_available = proactive_options and len(proactive_options) > 1
                is_ldmf_suggestion_used = SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM == content

                if changed_ldmf_option and not is_proactive_options_available and not is_ldmf_suggestion_used:
                    # The message from missing_data_response might contain a generic prompt that needs to be cleared
                    # if the system_message.content already contains a specific LDMF question.
                    ldmf_search_text = 'Could you tell me who the Lead Deloitte Member Firm is for this engagement?'
                    if missing_data_response.message and ldmf_search_text in system_message.content:
                        missing_data_response.message = ''
                    if status_is_missing:
                        missing_data_response.message = SystemReplyType.EXTRACTED_LDMF_NOT_VALID.message_text
                    system_message.content = f'{system_message.content}\n\n{missing_data_response.message}'
                    system_message.options = [changed_ldmf_option]
                elif missing_eng_dates:
                    system_message.content += f'\n\n{proactive_message_content}'
                    system_message.options = proactive_options
                elif (
                    is_ldmf_suggestion_used or intention == ConversationMessageIntention.USER_DENIAL
                ) and changed_ldmf_option:
                    system_message.content = system_message.content
                    system_message.options = [changed_ldmf_option]
                else:
                    if proactive_system_reply:
                        system_message.system_reply_type = proactive_system_reply
                    system_message.content = f'{system_message.content}\n\n{proactive_message_content}'
                    system_message.options = proactive_options

                if system_message.system_reply_type == SystemReplyType.NEED_INFO_CLIENT_NAME:
                    if proactive_chat_service.confirmed_fields_names:
                        system_message.content = f'{THANX_FOR_INFORMATION}\n{system_message.content}'

            else:
                # custom proactive messages for ConversationState.DATA_COMPLETE
                undefined_user_intention_on_data_complete = (
                    conversation_data.conversation.State == ConversationState.DATA_COMPLETE
                    and intention == ConversationMessageIntention.UNDEFINED
                )
                if (
                    system_message.system_reply_type in SystemReplyType.get_data_confirmation_replies()
                    or undefined_user_intention_on_data_complete
                ):
                    conversation_data = await self._fetch_conversation_data(conversation_id, token)
                    conversation: QualConversation = conversation_data.conversation

                    # Clear last_confirmed_field to prevent showing confirmation messages repeatedly
                    await self._clear_last_confirmed_field(conversation_id)

                    if str(conversation.State) == ConversationState.DATA_COMPLETE.value:
                        # same logic as in ConversationMessageProcessor._handle_data_complete_response
                        reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                        system_message.content = f'{system_message.content} {reply_type.message_text}'
                        system_message.system_reply_type = reply_type
                        system_message.suggested_prompts = [
                            SuggestedUserPrompt.NO_CREATE_MY_QUAL.value,
                        ]

            conversation_collects_country = (
                missing_data_response.conversation_state == ConversationState.COLLECTING_COUNTRY
                if missing_data_response
                else False
            )
            client_selected = selected_option and selected_option.type == OptionType.CLIENT_NAME
            if client_selected and not conversation_collects_country:
                # may modify system_message.options
                await self._handle_ldmf_country_options_after_client_name_confirmation(
                    system_message, proactive_chat_service, conversation_data
                )

            # Add suggested prompts when original system message content was None or empty
            not_intentions = [
                ConversationMessageIntention.CHANGE_ENGAGEMENT_DATES,
                ConversationMessageIntention.USER_DENIAL,
            ]
            if not system_message.suggested_prompts and intention not in not_intentions:
                # method possibly modifies system_message.suggested_prompts and system_message.system_reply_type
                await self._generate_and_set_suggested_prompts(
                    system_message, conversation_id, user_message, files, conversation_data
                )

        # Prepare & return response
        response = CombinedMessageSerializer(
            user=user_message,
            system=cast(SystemMessageSerializer, await self.conversation_message_repository.create(system_message))
            if system_message
            else None,
        )

        # Handle unified queue messaging for files and/or text content
        if files or (user_message.content and intention == ConversationMessageIntention.EXTRACTION):
            document_data = DocumentCreationRequest(
                conversation_id=user_message.conversation_id,
                files=files or [],
                message_id=response.user.id,
            )

            await self.extracted_data_service.ldmf_country_service.list(
                token
            )  # Initialize LDMF countries cache and upload to blob storage
            await self.extracted_data_service.industry_data_service.list(
                token
            )  # Initialize client industries cache and upload to blob storage
            await self.extracted_data_service.role_data_service.list(
                token
            )  # Initialize project roles cache and upload to blob storage
            await self.client_service_service.list(token)  # Initialize client services cache and upload to blob storage

            # Use unified approach: send text content for extraction along with files
            document_responses = await self.document_service.create_combined_message(
                document_data, user_message.content
            )

            if document_responses:
                response.files = document_responses

        if settings.append_collected_data_to_message_response and conversation_data:
            response.collected_data = conversation_data.aggregated_data.model_dump()

        return response

    async def generate_response_for_user_message(
        self, conversation_id: UUID, last_message: BaseMessageSerializer, token: str
    ) -> BaseMessageSerializer:
        """
        Generate a system response for the last user message, typically for polling clients.
        """
        statuses = await self.processing_message_repository.get_message_processing_statuses(last_message.id)

        if ProcessingStatus.ConfirmedFieldsChangeProhibited in statuses:
            logger.info(
                'ConfirmedFieldsChangeProhibited status detected in %s',
                'PromptMessageHandler.generate_response_for_user_message',
            )
            return await self._handle_confirmed_fields_change_prohibited_message(last_message.conversation_id)

        if ProcessingStatus.DocumentIsCorrupted in statuses:
            return await self._handle_corrupted_document_message(last_message)

        logger.debug('Last message is from user, generating system response for conversation %s', conversation_id)
        try:
            # Clean up corrupted document records if this is a file message
            await self._cleanup_corrupted_documents_if_needed(last_message)

            # Fetch all required data in parallel where possible
            conversation_data = await self._fetch_conversation_data(conversation_id, token)

            # Generate system message content and options
            message_content = await self._generate_message_content_and_options(
                conversation_id, conversation_data, last_message, token
            )

            # Create and return the system message
            system_message = await self.conversation_message_repository.create(
                message_content.to_message_validator(conversation_id, last_message)
            )
            logger.info('Generated system message %s for conversation %s', system_message.id, conversation_id)
            return system_message

        except Exception as e:
            logger.error('Failed to generate system message for conversation %s: %s', conversation_id, e)
            raise

    async def _ensure_previous_message_processed(self, conversation_id: UUID) -> None:
        """
        Ensure the previous message has been fully processed before proceeding.
        """
        try:
            last_message = await self.conversation_message_repository.get_last(conversation_id)
        except EntityNotFoundError:
            last_message = None

        if (
            last_message
            and last_message.role == MessageRole.USER
            and last_message.type
            in (
                MessageType.FILE,
                MessageType.TEXT_WITH_FILE,
            )
        ):
            statuses = await self.processing_message_repository.get_message_processing_statuses(last_message.id)
            if ProcessingStatus.DocumentIsCorrupted in statuses:
                message_data = MessageValidator(
                    conversation_id=last_message.conversation_id,
                    role=MessageRole.SYSTEM,
                    type=MessageType.TEXT,
                    content='',
                    selected_option=None,
                    files=None,
                    system_reply_type=None,
                )

                await self.conversation_message_repository.create(message_data)

            elif ProcessingStatus.DocumentExtractionCompleted not in statuses:
                raise ConversationDataInconsistencyError(
                    'Cannot create a new message until the previous one has been processed'
                )

    async def autoconfirm_data_from_kxdash(
        self, conversation_data: ConversationData, conversation_id: UUID, token: str
    ):
        aggregated_data = conversation_data.aggregated_data
        confirmed_data = conversation_data.confirmed_data
        to_update, updated_confirm_data = await self._update_fields_from_aggregated_data(
            aggregated_data, confirmed_data, token, True
        )

        if to_update:
            logger.info(
                'Updating confirmed data and setting state to COLLECTING_CLIENT_NAME for conversation %s',
                conversation_id,
            )
            next_state = updated_confirm_data.get_current_conversation_state()
            await self.conversation_repository.update_confirmed_data_and_state(
                public_id=conversation_id,
                confirmed_data=updated_confirm_data,
                state=next_state,
            )

    async def _generate_and_set_suggested_prompts(
        self,
        system_message: MessageValidator,
        conversation_id: UUID,
        user_message: UserMessageSerializer,
        files: list[UploadFile] | None,
        conversation_data: ConversationData,
    ):
        system_reply_type_generator = SystemMessageReplyTypeGenerator(
            conversation_data=conversation_data,
        )
        reply_type = system_reply_type_generator.generate_system_reply_type()
        if reply_type:
            suggested_prompts = await self.get_suggested_prompts(
                conversation_id=conversation_id,
                conversation_data=conversation_data,
                user_message=user_message,
                files=files,
                intention=ConversationMessageIntention.EXTRACTION,
                current_reply_type=reply_type,
                called_from='PromptMessageHandler._generate_and_set_suggested_prompts',
            )
            system_message.system_reply_type = reply_type
            system_message.suggested_prompts = [i.value for i in suggested_prompts]

    async def _handle_ldmf_country_options_after_client_name_confirmation(
        self,
        system_message: MessageValidator,
        proactive_chat_service: ProactiveChatService,
        conversation_data: ConversationData,
    ) -> None:
        """
        Handles setting LDMF country options when client name is confirmed and
        there are multiple unconfirmed LDMF countries.
        """
        confirmed_data = conversation_data.confirmed_data
        aggregated_data = conversation_data.aggregated_data
        should_set_ldmf_country_options = (
            confirmed_data.client_name and not confirmed_data.ldmf_country and len(aggregated_data.ldmf_country) > 1
        )

        if should_set_ldmf_country_options:
            if (next_field := proactive_chat_service.next_required_field) != RequiredField.LDMF_COUNTRY:
                raise RuntimeError(
                    'User has confirmed client name and specified more than 1 LDMF country, so the next required field '
                    f'is expected to be {RequiredField.LDMF_COUNTRY}, got {next_field} instead. Please validate the logic.'
                )

            if system_message.options and system_message.options != self._EMPTY_LDMF_COUNTRY_OPTION:
                raise RuntimeError(
                    f'System message options were previously modified, and contain: {system_message.options}. Please validate the logic.'
                )

            system_message.options = aggregated_data.ldmf_countries_as_options

    async def get_suggested_prompts(
        self,
        conversation_id: UUID,
        conversation_data: ConversationData,
        user_message: UserMessageSerializer,
        files: list[UploadFile] | None,
        intention: ConversationMessageIntention,
        current_reply_type: SystemReplyType,
        called_from: str,
    ) -> list[SuggestedUserPrompt]:
        dash_task_activity_id: int | None = await self.conversation_repository.get_dash_task_activity_id(
            conversation_id
        )

        # Generate suggested replies for the user
        suggested_prompts_generator = SuggestedPromptsGenerator(
            conversation_id=conversation_id,
            conversation_data=conversation_data,
            conversation_message_history=conversation_data.conversation_message_history,
            user_message=user_message,
            intention=intention,
            files=files,
            dash_task_activity_id=dash_task_activity_id,
            current_reply_type=current_reply_type,
            called_from=called_from,
        )
        return await suggested_prompts_generator.run()

    async def _handle_confirmed_fields_change_prohibited_message(self, conversation_id: UUID) -> BaseMessageSerializer:
        """
        Handles the case where the last message is associated with confirmed fields change prohibited.
        It builds and creates a system message with error details.
        """
        reply_type = SystemReplyType.CONFIRMED_FIELDS_CHANGE_PROHIBITED
        return await self.conversation_message_repository.create(
            MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.ERROR,
                content=reply_type.message_text,
                system_reply_type=reply_type,
            )
        )

    async def _handle_corrupted_document_message(self, last_message: BaseMessageSerializer) -> BaseMessageSerializer:
        """
        Handles the case where the last message is associated with corrupted documents.
        It builds and creates a system message with error details.
        """
        message_data = await self._build_corrupted_file_error_message(last_message)
        return await self.conversation_message_repository.create(message_data)

    async def _build_corrupted_file_error_message(self, last_message: BaseMessageSerializer) -> MessageValidator:
        """
        Builds a system message for a corrupted file error, including details about which files failed.
        """
        total_docs = await self.document_db_repository.get_total_document_count_for_message(last_message.id)
        corrupted_filenames = await self.processing_message_repository.get_corrupted_filenames_for_message(
            last_message.id
        )
        total_corrupted = len(corrupted_filenames)

        suggested_prompts = []
        if total_docs > total_corrupted:
            suggested_prompts.append(SuggestedUserPrompt.CONTINUE_WITHOUT_ERROR_FILE.value)

        utils_map = {
            (1, 1): CorruptedUtilsTemplate.ONE,
            (1, 2): CorruptedUtilsTemplate.ONE_OF_UPLOADED,
            (1, 3): CorruptedUtilsTemplate.ONE_OF_UPLOADED,
            (2, 2): CorruptedUtilsTemplate.ALL,
            (2, 3): CorruptedUtilsTemplate.TWO_OF_UPLOADED,
            (3, 3): CorruptedUtilsTemplate.ALL,
        }
        utils_part = utils_map.get((total_corrupted, total_docs), CorruptedUtilsTemplate.ALL)

        file_names = ', '.join([f'<b>{truncate_filename(name)}</b>' for name in corrupted_filenames])

        content = SystemReplyType.CORRUPTED_FILE.message_text.format(
            file_names=file_names, support_url=settings.support_url, utils_part=str(utils_part)
        )

        return MessageValidator(
            conversation_id=last_message.conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.ERROR,
            content=content,
            selected_option=None,
            files=None,
            system_reply_type=SystemReplyType.CORRUPTED_FILE,
            suggested_prompts=suggested_prompts,
        )

    def _convert_to_option_objects(
        self,
        raw_options: List | None,
        conversation_state: ConversationState,
    ) -> List[Option]:
        """Convert raw options to proper option objects based on conversation state."""
        if not raw_options and conversation_state == ConversationState.COLLECTING_DATES:
            raw_options = [(None, None)]
        elif not raw_options or not conversation_state:
            return []

        if conversation_state == ConversationState.COLLECTING_CLIENT_NAME:
            return [ClientNameOption(client_name=name) for name in raw_options]
        elif conversation_state == ConversationState.COLLECTING_COUNTRY:
            return [LDMFCountryOption(ldmf_country=name) for name in raw_options]
        elif conversation_state == ConversationState.COLLECTING_DATES:
            return [DatePickerOption(start_date=start_date, end_date=end_date) for start_date, end_date in raw_options]
        return []

    async def _get_system_message_with_result(
        self,
        message_data: MessageValidator,
        processing_result: ConversationMessageProcessingResult,
        suggested_prompts: Sequence[SuggestedUserPrompt],
        confirmed_data: ConfirmedData,
    ) -> MessageValidator:
        """
        Create a system message based on the message processing result.
        """
        options = []
        reply_type = processing_result.system_reply_type
        content = processing_result.system_reply
        # Check if we have options from the processing result
        if 'options' in processing_result.data and processing_result.data['options']:
            # Check conversation state to determine option type
            conversation_state = processing_result.data.get('conversation_state')
            if conversation_state:
                options = self._convert_to_option_objects(processing_result.data['options'], conversation_state)

        if (
            confirmed_data.required_fields_are_complete
            and processing_result.system_reply_type == SystemReplyType.CLIENT_NAME_CONFIRMED
        ):
            confirmation_message_text = reply_type.message_text.format(client_name=confirmed_data.client_name)
            reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
            content = f'{confirmation_message_text} {reply_type.message_text}'
        if (
            confirmed_data.required_fields_are_complete
            and processing_result.system_reply_type == SystemReplyType.CONFIRMED_FIELDS_READY
            and confirmed_data.last_confirmed_field == RequiredField.CLIENT_NAME
        ):
            reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED_LAST_FIELD
            content = reply_type.message_text.format(client_name=confirmed_data.client_name)

        return MessageValidator(
            conversation_id=message_data.conversation_id,
            role=MessageRole.SYSTEM,
            type=message_data.type,
            content=content,
            system_reply_type=reply_type,
            options=options,
            suggested_prompts=[i.value for i in suggested_prompts],
        )

    async def _handle_confirmed_data_update(self, public_id: UUID, aggregated_data: AggregatedData, token: str) -> None:
        """
        Handle confirmed data logic based on aggregated data state.
        """
        try:
            # Fetch confirmed data from existing conversation
            confirmed_data = await self.conversation_repository.get_confirmed_data(public_id)

            # Check if we need to update confirmed data
            should_update = False

            # If confirmed data is empty and aggregated has all required fields with single values
            if confirmed_data.is_empty and aggregated_data.is_complete:
                logger.info('Confirmed data is empty and aggregated data is complete for conversation %s', public_id)
                # Apply all fields from aggregated except client_name
                should_update, confirmed_data = await self._update_fields_from_aggregated_data(
                    aggregated_data, confirmed_data, token
                )

            elif confirmed_data.is_empty and not aggregated_data.is_complete:
                logger.info(
                    'Confirmed data is empty and aggregated data is not complete for conversation %s', public_id
                )
                should_update, confirmed_data = await self._update_fields_from_aggregated_data(
                    aggregated_data, confirmed_data, token
                )

            # If confirmed data has some fields - update from aggregated
            elif not confirmed_data.is_empty and not confirmed_data.required_fields_are_complete:
                logger.info('Confirmed data has some fields, updating from aggregated for conversation %s', public_id)

                # Update ldmf_country if not confirmed and available in aggregated
                if (
                    not confirmed_data.ldmf_country
                    and aggregated_data.ldmf_country
                    and len(aggregated_data.ldmf_country) == 1
                ):
                    verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                        aggregated_data.ldmf_country[0], token
                    )
                    if verified_countries and len(verified_countries) == 1:
                        confirmed_data.ldmf_country = verified_countries[0]
                        should_update = True

                # Update date_intervals if not confirmed and available in aggregated
                if (
                    not confirmed_data.date_intervals
                    and aggregated_data.date_intervals
                    and aggregated_data.date_intervals_original
                    and len(aggregated_data.date_intervals) == 1
                    and not aggregated_data.more_than_two_dates  # Don't auto-confirm if multiple dates were detected
                ):
                    start_date, end_date = aggregated_data.date_intervals[0]
                    start_date_original, end_date_original = aggregated_data.date_intervals_original[0]
                    if await self._is_date_unambiguous_and_complete(
                        start_date=start_date,
                        end_date=end_date,
                        start_date_original=start_date_original,
                        end_date_original=end_date_original,
                    ):
                        confirmed_data.date_intervals = (start_date, end_date)
                        should_update = True

                # Update objective_and_scope if not confirmed and available in aggregated
                if not confirmed_data.objective_and_scope and aggregated_data.objective_and_scope:
                    confirmed_data.objective_and_scope = aggregated_data.objective_and_scope
                    should_update = True

                # Update outcomes if not confirmed and available in aggregated
                if not confirmed_data.outcomes and aggregated_data.outcomes:
                    confirmed_data.outcomes = aggregated_data.outcomes
                    should_update = True

            # If confirmed data has all fields - do nothing
            elif confirmed_data.required_fields_are_complete:
                logger.info('Confirmed data is complete, no update needed for conversation %s', public_id)
                return

            # Save to database and set state to collecting client name if confirmed data was updated
            if should_update:
                logger.info(
                    'Updating confirmed data and setting state to COLLECTING_CLIENT_NAME for conversation %s', public_id
                )
                await self.conversation_repository.update_confirmed_data_and_state(
                    public_id=public_id,
                    confirmed_data=confirmed_data,
                    state=ConversationState.COLLECTING_CLIENT_NAME,
                )

        except Exception:
            logger.exception('Error handling confirmed data update for conversation %s', public_id)
            raise

    async def _update_fields_from_aggregated_data(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, token: str, is_dash: bool = False
    ) -> tuple[bool, ConfirmedData]:
        should_update = False

        if aggregated_data.is_client_name_complete and is_dash:
            confirmed_data.client_name = aggregated_data.client_name[0]
            should_update = True

        if aggregated_data.ldmf_country and len(aggregated_data.ldmf_country) == 1:
            verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                aggregated_data.ldmf_country[0], token
            )
            if verified_countries and len(verified_countries) == 1:
                confirmed_data.ldmf_country = verified_countries[0]
                should_update = True

        if (
            aggregated_data.date_intervals
            and aggregated_data.date_intervals_original
            and len(aggregated_data.date_intervals) == 1
            and not aggregated_data.more_than_two_dates  # Don't auto-confirm if multiple dates were detected
        ):
            start_date, end_date = aggregated_data.date_intervals[0]
            start_date_original, end_date_original = aggregated_data.date_intervals_original[0]
            if await self._is_date_unambiguous_and_complete(
                start_date=start_date,
                end_date=end_date,
                start_date_original=start_date_original,
                end_date_original=end_date_original,
            ):
                confirmed_data.date_intervals = (start_date, end_date)
                should_update = True

        if aggregated_data.objective_and_scope:
            confirmed_data.objective_and_scope = aggregated_data.objective_and_scope
            should_update = True

        if aggregated_data.outcomes:
            confirmed_data.outcomes = aggregated_data.outcomes
            should_update = True

        return should_update, confirmed_data

    async def _is_date_unambiguous_and_complete(
        self,
        start_date: str | None,
        end_date: str | None,
        start_date_original: str | None,
        end_date_original: str | None,
    ) -> bool:
        """Check if the date is unambiguous and complete."""
        if not start_date or not end_date or not start_date_original or not end_date_original:
            return False

        processed_start_date = datetime.date.fromisoformat(start_date)
        processed_end_date = datetime.date.fromisoformat(end_date)

        for date, date_original in (
            (processed_start_date, start_date_original),
            (processed_end_date, end_date_original),
        ):
            if (
                date
                and date.day <= 12
                and not (
                    await self.date_validator_service.date_is_text(user_message=date_original, date=date.isoformat())
                )
            ):
                return False

        return True

    async def _get_dash_discard_response(
        self,
        user_message: UserMessageSerializer,
        processing_result: ConversationMessageProcessingResult,
        suggested_prompts: Sequence[SuggestedUserPrompt],
    ) -> CombinedMessageSerializer:
        message = MessageValidator(
            conversation_id=user_message.conversation_id,
            role=MessageRole.SYSTEM,
            type=user_message.type,
            content=processing_result.system_reply,
            suggested_prompts=[i.value for i in suggested_prompts],
            system_reply_type=processing_result.system_reply_type,
        )

        response = CombinedMessageSerializer(
            user=user_message,
            system=cast(SystemMessageSerializer, await self.conversation_message_repository.create(message)),
        )

        return response

    async def _fetch_conversation_data(self, conversation_id: UUID, token: str) -> ConversationData:
        """
        Fetch all required conversation data efficiently.
        """
        # Get aggregated data first as it's needed for confirmed data update
        aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)

        # Handle confirmed data logic
        await self._handle_confirmed_data_update(conversation_id, aggregated_data, token)

        # Fetch remaining data - these could potentially be done in parallel
        conversation_message_history = await self.conversation_message_repository.get_combined_history(conversation_id)
        confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
        conversation = await self.conversation_repository.get(conversation_id)

        if not conversation:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        return ConversationData(
            conversation_message_history=conversation_message_history,
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )

    async def _generate_message_content_and_options(
        self,
        conversation_id: UUID,
        conversation_data: ConversationData,
        last_message: BaseMessageSerializer,
        token: str,
    ) -> MessageContent:
        """
        Generate system message content and options based on conversation state.
        """
        # Check if we should use client name single confirmation message
        if conversation_data.should_use_client_confirmation:
            return await self._generate_client_confirmation_content(conversation_data, last_message, token)
        else:
            return await self._generate_standard_system_content(conversation_id, conversation_data, last_message, token)

    async def _generate_client_confirmation_content(
        self,
        conversation_data: ConversationData,
        last_message: BaseMessageSerializer,
        token: str,
    ) -> MessageContent:
        """
        Generate content for client name confirmation message.
        """

        conversation: QualConversation = conversation_data.conversation
        conversation_id: UUID = conversation.PublicId  # type: ignore[reportAssignmentType]

        client_handling_response: FieldHandlerResponse = await self.extracted_data_service.single_value_validation(
            field=RequiredField.CLIENT_NAME,
            field_value=conversation_data.confirmed_data.client_name
            or conversation_data.aggregated_data.client_name[0],
            token=token,
            called_from='PromptMessageHandler._generate_client_confirmation_content',
        )

        client_handling_reply_type: SystemReplyType | None = client_handling_response.system_reply_type
        if client_handling_reply_type == SystemReplyType.CLIENT_NOT_FOUND:
            reply_type = client_handling_reply_type
            all_fields_extracted_template = ALL_REQUIRED_FIELDS_EXTRACTED_CLIENT_NOT_FOUND
        else:
            reply_type = SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION
            all_fields_extracted_template = ALL_REQUIRED_FIELDS_EXTRACTED_DOCS

        # Get filenames from the previous user message
        filenames = await self.document_db_repository.get_filenames_for_message(last_message.id)

        if filenames:
            document_description = self.document_service.format_document_description(filenames)
            formatted_content = all_fields_extracted_template.format(
                client_name=conversation_data.aggregated_data.client_name[0], document_description=document_description
            )
        else:
            formatted_content = reply_type.message_text.format(
                client_name=conversation_data.aggregated_data.client_name[0]
            )

        suggested_prompts = await self.get_suggested_prompts(
            conversation_id=conversation_id,
            conversation_data=conversation_data,
            user_message=cast(UserMessageSerializer, last_message),
            files=None,
            intention=ConversationMessageIntention.EXTRACTION,
            current_reply_type=reply_type,
            called_from=self._generate_client_confirmation_content.__name__,
        )
        logger.info(
            '%s system_reply_type detected in %s',
            reply_type,
            'PromptMessageHandler._generate_client_confirmation_content',
        )
        return MessageContent(
            formatted_content=formatted_content,
            reply_type=reply_type,
            extracted_options=[],
            suggested_prompts=suggested_prompts,
        )

    async def _generate_standard_system_content(
        self,
        conversation_id: UUID,
        conversation_data: ConversationData,
        last_message: BaseMessageSerializer,
        token: str,
    ) -> MessageContent:
        """
        Generate standard system message content and options.
        """
        missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
            conversation_id=conversation_id,
            token=token,
            confirmed_data=conversation_data.confirmed_data,
            called_from='PromptMessageHandler._generate_standard_system_content',
            conversation_data=conversation_data,
        )
        # Format the extracted data message
        formatted_content, reply_type = self.system_message_service.generate_system_message(
            conversation_data, missing_data_response
        )
        extracted_options = self.system_message_service.generate_options(
            conversation_data.aggregated_data,
            conversation_data.confirmed_data,
            ConversationState(conversation_data.conversation.State),
        )

        # Use date options from missing_data_response if available (for date confirmation)
        if not extracted_options and missing_data_response.conversation_state == ConversationState.COLLECTING_DATES:
            extracted_options = (
                self._convert_to_option_objects(missing_data_response.options, missing_data_response.conversation_state)
                if missing_data_response.options
                else [DatePickerOption(start_date=None, end_date=None)]
            )

        # Use LDMF country options from missing_data_response if available (for country input)
        if not extracted_options and missing_data_response.conversation_state == ConversationState.COLLECTING_COUNTRY:
            extracted_options = (
                self._convert_to_option_objects(missing_data_response.options, missing_data_response.conversation_state)
                if missing_data_response.options
                else self._EMPTY_LDMF_COUNTRY_OPTION
            )

        if missing_data_response.message and formatted_content != missing_data_response.message:
            formatted_content = missing_data_response.message
            if missing_data_response.reply_type:
                if reply_type != missing_data_response.reply_type:
                    logger.warning(
                        'Original reply type %s will be overwritten with %s',
                        reply_type,
                        missing_data_response.reply_type,
                    )

                reply_type = missing_data_response.reply_type

        suggested_prompts = await self.get_suggested_prompts(
            conversation_id=conversation_id,
            conversation_data=conversation_data,
            user_message=cast(UserMessageSerializer, last_message),
            files=None,
            intention=ConversationMessageIntention.EXTRACTION,
            current_reply_type=reply_type,
            called_from=self._generate_standard_system_content.__name__,
        )

        # enchanced proactive message
        proactive_chat_service = ProactiveChatService(
            conversation_data=conversation_data,
            system_reply_type=reply_type,
            is_dash_task=False,
        )
        if reply_type == SystemReplyType.NEED_INFO_CLIENT_NAME:
            if proactive_chat_service.confirmed_fields_names:
                formatted_content = f'{THANX_FOR_INFORMATION}\n{formatted_content}'
        if reply_type == SystemReplyType.NEED_INFO_LDMF_COUNTRY:
            if conversation_data.confirmed_data.last_confirmed_field == RequiredField.CLIENT_NAME:
                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                confimation_message_text = reply_type.message_text.format(
                    client_name=conversation_data.confirmed_data.client_name
                )
                formatted_content = f'{confimation_message_text}\n\n{formatted_content}'

        logger.info(
            '%s system_reply_type detected in %s',
            reply_type,
            'PromptMessageHandler._generate_standard_system_content',
        )
        return MessageContent(
            formatted_content=formatted_content,
            reply_type=reply_type,
            extracted_options=extracted_options,
            suggested_prompts=suggested_prompts,
        )

    async def _clear_last_confirmed_field(self, conversation_id: UUID) -> None:
        """
        Clear the last_confirmed_field to prevent showing confirmation messages repeatedly.
        """
        try:
            # Get conversation directly and read confirmed data from DB model field
            conversation = await self.conversation_repository.get(conversation_id)
            if not conversation:
                logger.warning(f'Conversation {conversation_id} not found')
                return

            # Parse confirmed data from JSON field
            current_confirmed_data = ConfirmedData.from_json_string(cast(str, conversation.ConfirmedData))
            current_confirmed_data.last_confirmed_field = None

            # Update with cleared field, maintaining current state
            current_state = ConversationState(conversation.State)
            await self.conversation_repository.update_confirmed_data_and_state(
                public_id=conversation_id, confirmed_data=current_confirmed_data, state=current_state
            )

            logger.debug('Successfully cleared last_confirmed_field for conversation ID: %s', conversation_id)
        except Exception as e:
            logger.error('Error clearing last_confirmed_field for conversation %s: %s', conversation_id, e)

    async def _cleanup_corrupted_documents_if_needed(self, last_message: BaseMessageSerializer) -> None:
        """
        Clean up corrupted document records for file messages.
        """
        # Only process file messages
        if last_message.type not in (MessageType.FILE, MessageType.TEXT_WITH_FILE):
            return

        try:
            # Check processing statuses for this message
            statuses = await self.processing_message_repository.get_message_processing_statuses(last_message.id)

            # If there are corrupted documents, clean them up
            if ProcessingStatus.DocumentIsCorrupted in statuses:
                removed_count = await self.document_db_repository.remove_corrupted_documents_for_message(
                    last_message.id
                )
                if removed_count > 0:
                    logger.info(
                        'Cleaned up %d corrupted document records for message %s', removed_count, last_message.id
                    )

        except Exception as e:
            # Log the error but don't fail the entire request
            logger.error('Error cleaning up corrupted documents for message %s: %s', last_message.id, e)
