import logging
from uuid import UUID

from fastapi import UploadFile

from constants.engagement import EngagementMessageIntention
from constants.message import MessageR<PERSON>, MessageType, PageType, QualFieldName, SystemReplyType
from constants.prompt import prompt_templates
from repositories import ConversationMessageRepository, ConversationRepository, FieldRepository, OpenAIRepository
from schemas import (
    CombinedMessageSerializer,
    Command,
    EngagementFieldModificationResponse,
    EngagementMessageIntentClassifierServiceResponse,
    MessageValidator,
    Option,
    QualFields,
    SystemMessageDetails,
    SystemMessageSerializer,
    TextField,
    UserMessageSerializer,
)
from schemas.engagement_message import EngagementIntentProcessorResponse
from services.command import CommandService
from services.engagement_field_modification import EngagementFieldModificationService
from services.engagement_intent_processor import EngagementIntentProcessor
from services.intent_classifier import IntentClassifierService
from services.message_handlers.base import MessageHandler


__all__ = ['EngagementDescriptionMessageHandler']

logger = logging.getLogger(__name__)


class EngagementDescriptionMessageHandler(MessageHandler):
    def __init__(
        self,
        conversation_message_repository: ConversationMessageRepository,
        openai_repository: OpenAIRepository,
        engagement_intent_processor: EngagementIntentProcessor,
        engagement_field_modification_service: EngagementFieldModificationService,
        command_service: CommandService,
        field_repository: FieldRepository,
        conversation_repository: ConversationRepository,
    ):
        # Core repositories and services
        self.conversation_message_repository = conversation_message_repository
        self.openai_repository = openai_repository
        self.engagement_intent_processor = engagement_intent_processor
        self.engagement_field_modification_service = engagement_field_modification_service
        self.command_service = command_service
        self.field_repository = field_repository
        self.conversation_repository = conversation_repository

        # Intent classifier setup
        self.engagement_intent_classifier_service = IntentClassifierService(
            openai_service=openai_repository,
            system_prompt_template=prompt_templates.engagement_description_page_intention.SYSTEM,
            user_prompt_template=prompt_templates.engagement_description_page_intention.USER,
            intentions=prompt_templates.engagement_description_page_intention.INTENTIONS,
        )

    async def handle(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        command: Command | None,
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer:
        page_type = PageType.ENGAGEMENT_DESCRIPTION
        user_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content=content,
            selected_option=selected_option,
            command=command,
            files=files,
            system_reply_type=None,
            page_type=page_type,
        )
        user_message = await self.conversation_message_repository.create(user_message_to_persist)

        # Initial anonymization of all fields
        await self._anonymize_all_fields(conversation_id)

        intent_classification_response: EngagementMessageIntentClassifierServiceResponse = (
            await self.engagement_intent_classifier_service.classify_intent(
                user_message=content,
                response_cls=EngagementMessageIntentClassifierServiceResponse,
                intention_enum=EngagementMessageIntention,
            )
        )
        intent = intent_classification_response.intention

        # Check if this is a field modification intent
        if command:
            system_message = await self.command_service.process_command(command, content, conversation_id)
            system_content = system_message.content
            message_type = system_message.type
            is_error = system_message.is_error
            system_reply_type = system_message.system_reply_type
            qual_fields = system_message.qual_fields
            is_undoable = system_message.is_undoable

        elif self._is_field_modification_intent(intent):
            # Route to field modification service
            modification_result = await self.engagement_field_modification_service.modify_field(
                conversation_id=conversation_id,
                intent=intent,
                user_request=content,
            )
            processed_result = self.engagement_intent_processor.process_intent(intent)
            reply_type = self.engagement_intent_processor.process_intent_reply_type(intent)
            system_message_details = self._get_system_message_details(modification_result, processed_result)

            system_content = system_message_details.content
            message_type = system_message_details.message_type
            is_error = message_type == MessageType.ERROR
            system_reply_type = SystemReplyType.ENGAGEMENT_DETAILS_FIELD_ERROR if is_error else reply_type
            qual_fields = (
                QualFields(
                    **{
                        modification_result.field_name: TextField(
                            context=modification_result.updated_content,
                            snippet=modification_result.updated_content,
                        )
                    }
                )
                if modification_result.success
                and modification_result.field_name
                and modification_result.updated_content
                else None
            )
            is_undoable = True
        else:
            # Use existing intent processor for non-field-modification intents
            processed_result = self.engagement_intent_processor.process_intent(intent)
            reply_type = self.engagement_intent_processor.process_intent_reply_type(intent)

            system_content = processed_result.response
            message_type = MessageType.TEXT
            system_reply_type = reply_type
            is_error = False
            qual_fields = None
            is_undoable = False

        system_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=message_type,
            content=system_content,
            system_reply_type=system_reply_type,
            is_error=is_error,
            page_type=page_type,
            qual_fields=qual_fields,
            is_undoable=is_undoable,
        )
        system_message = await self.conversation_message_repository.create(system_message_to_persist)

        return CombinedMessageSerializer(
            user=UserMessageSerializer.model_validate(user_message),
            system=SystemMessageSerializer.model_validate(system_message),
        )

    async def _anonymize_all_fields(self, conversation_id: UUID) -> None:
        """
        Anonymize all relevant fields by replacing the client name.
        """
        try:
            confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
            client_name = confirmed_data.client_name
            if not client_name:
                return

            fields_to_update = [
                QualFieldName.TITLE,
                QualFieldName.BUSINESS_ISSUES,
                QualFieldName.SCOPE_APPROACH,
                QualFieldName.VALUE_DELIVERED,
                QualFieldName.ENGAGEMENT_SUMMARY,
                QualFieldName.ONE_LINE_DESCRIPTION,
            ]

            for field_name in fields_to_update:
                latest_field = await self.field_repository.get_latest_value(conversation_id, field_name)
                if latest_field and latest_field.FieldValue is not None:
                    original_text = str(latest_field.FieldValue)
                    updated_text = original_text.replace(client_name, 'the client')
                    if original_text != updated_text:
                        await self.field_repository.create(
                            conversation_id=conversation_id,
                            field_name=field_name,
                            field_value=updated_text,
                            formatted_field_value=updated_text,
                        )
        except Exception as e:
            logger.error(f'Anonymization failed for conversation {conversation_id}: {e}')

    def _is_field_modification_intent(self, intent: EngagementMessageIntention) -> bool:
        """
        Check if the intent is for field modification.

        Args:
            intent: The classified engagement message intention

        Returns:
            True if the intent is for field modification, False otherwise
        """
        field_modification_intents = {
            EngagementMessageIntention.BUSINESS_ISSUES,
            EngagementMessageIntention.ENGAGEMENT_TITLE,
            EngagementMessageIntention.SCOPE_APPROACH,
            EngagementMessageIntention.VALUE_DELIVERED_IMPACT,
            EngagementMessageIntention.ENGAGEMENT_SUMMARY,
            EngagementMessageIntention.ONE_LINE_DESCRIPTION,
        }
        return intent in field_modification_intents

    def _get_system_message_details(
        self,
        modification_result: EngagementFieldModificationResponse,
        processed_result: EngagementIntentProcessorResponse,
    ) -> SystemMessageDetails:
        """
        Generate system response message details based on field modification result.

        Args:
            modification_result: Result from the field modification service

        Returns:
            SystemMessageDetails object containing content string, message type, and system reply type
        """
        if modification_result.success:
            content = processed_result.response
            return SystemMessageDetails(
                content=content,
                message_type=MessageType.TEXT,
            )
        else:
            error_message = modification_result.error or 'Unknown error occurred'
            content = f'❌ Failed to update the field: {error_message}. Please try again or rephrase your request.'
            return SystemMessageDetails(
                content=content,
                message_type=MessageType.ERROR,
            )
