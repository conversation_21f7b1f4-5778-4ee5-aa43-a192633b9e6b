import logging
from typing import cast

from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)

from config import settings
from constants.message import (
    DETECT_LANGUAGE_SYSTEM_PROMPT,
    DETECT_LANGUAGE_USER_PROMPT,
    TRANSLATE_TO_ENGLISH_SYSTEM_PROMPT,
    TRANSLATE_TO_ENGLISH_USER_PROMPT,
)
from repositories import OpenAIRepository
from schemas.translation import TranslationLLMResponse


__all__ = ['TranslationService']


logger = logging.getLogger(__name__)


class TranslationService:
    """Determines original language user messages and translates it to english"""

    DEFAULT_TEMPERATURE: float = settings.openai.default_temperature
    TRANSLATION_TEMPERATURE: float = 0.3

    def __init__(self, openai_service: OpenAIRepository):
        """
        Initialize the TranslationService with an OpenAI repository.

        Args:
            openai_service: An instance of OpenAIRepository to interact with the OpenAI API.
        """
        self.openai_service: OpenAIRepository = openai_service

    async def get_translated_text(self, text: str) -> str:
        """
        Translates user's message or other content to English.

        Args:
            text: The user's message or other content.

        Returns:
            The translated message in English.
        """
        language = await self.detect_language(text)
        if language.lower() == 'english':
            return text
        return await self.translate_text(language, text)

    async def detect_language(
        self,
        text: str,
    ) -> str:
        """
        Extracts the original language of the text given using an LLM.

        Args:
            text: The user's message or other content.

        Returns:
            The validated language.
        """

        # Prepare the messages
        system_message = self._get_system_message(DETECT_LANGUAGE_SYSTEM_PROMPT)
        user_message_param = self._get_user_message(
            DETECT_LANGUAGE_USER_PROMPT.format(
                text=text,
            )
        )

        # Call the OpenAI API
        response = await self.openai_service.generate_chat_completion(
            messages=[
                cast(ChatCompletionMessageParam, system_message),
                cast(ChatCompletionMessageParam, user_message_param),
            ],
            temperature=self.DEFAULT_TEMPERATURE,
            response_format=TranslationLLMResponse,
        )

        if not isinstance(response, TranslationLLMResponse):
            raise RuntimeError(f'Invalid response from model, expected {TranslationLLMResponse}, got {type(response)}')

        return response.text

    async def translate_text(self, language, text: str) -> str:
        """
        Translates any given plain text from given language to english using an LLM.

        Args:
            language: original language of user message or other text
            text: The original content.

        Returns:
            Text translated into english.
        """
        # Prepare the messages
        system_message = self._get_system_message(TRANSLATE_TO_ENGLISH_SYSTEM_PROMPT)
        user_message_param = self._get_user_message(
            TRANSLATE_TO_ENGLISH_USER_PROMPT.format(
                language=language,
                text=text,
            )
        )

        # Call the OpenAI API
        response = await self.openai_service.generate_chat_completion(
            messages=[
                cast(ChatCompletionMessageParam, system_message),
                cast(ChatCompletionMessageParam, user_message_param),
            ],
            temperature=self.TRANSLATION_TEMPERATURE,
            response_format=TranslationLLMResponse,
        )

        if not isinstance(response, TranslationLLMResponse):
            raise RuntimeError(f'Invalid response from model, expected {TranslationLLMResponse}, got {type(response)}')

        return response.text

    @staticmethod
    def _get_system_message(message: str) -> ChatCompletionSystemMessageParam:
        """Create a system message for the chat completion."""
        return {'role': 'system', 'content': message}

    @staticmethod
    def _get_user_message(message: str) -> ChatCompletionUserMessageParam:
        """Create a user message for the chat completion."""
        return {'role': 'user', 'content': message}
