import logging
from uuid import UUID

from constants.extracted_data import Confirmed<PERSON><PERSON><PERSON><PERSON><PERSON>, ConversationState, FieldStatus, RequiredField
from constants.message import MessageRole, MessageType, SuggestedUserPrompt, SystemReplyType
from repositories import ConversationRepository
from schemas import ClientNameOption, MessageValidator
from schemas.extracted_data import FieldHandlerResponse
from services.extracted_data import ExtractedDataService

from .base import BaseOptionHandler


__all__ = ['ClientNameOptionHandlerService']


logger = logging.getLogger(__name__)


class ClientNameOptionHandlerService(BaseOptionHandler[ClientNameOption]):
    """Service for handling client name option selections."""

    def __init__(
        self,
        extracted_data_service: ExtractedDataService,
        conversation_repository: ConversationRepository,
    ):
        self.extracted_data_service = extracted_data_service
        self.conversation_repository = conversation_repository

    async def handle(
        self,
        selected_option: ClientNameOption,
        conversation_id: UUID,
        token: str | None = None,
    ) -> MessageValidator:
        """
        Handle client name selection from options.

        Args:
            selected_option: The selected client name option
            conversation_id: The conversation ID
            token: The user's token

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        if token is None:
            raise ValueError('Token is required for client name handling')

        try:
            logger.debug(
                'Handling client name selection: %s for conversation: %s', selected_option.client_name, conversation_id
            )

            missing_fields: FieldHandlerResponse = await self.extracted_data_service.single_value_validation(
                field=RequiredField.CLIENT_NAME,
                field_value=selected_option.client_name,
                token=token,
                called_from='ClientNameOptionHandlerService.handle',
            )

            exact_match = False
            if missing_fields.field_status == FieldStatus.MULTIPLE and missing_fields.options:
                exact_match = selected_option.client_name in missing_fields.options
            move_to_next_field = not missing_fields.needs_confirmation or exact_match

            if move_to_next_field:
                # Update confirmed data with the selected client name
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=str(ConfirmedDataFields.CLIENT_NAME),
                    field_value=selected_option.client_name,
                    state=ConversationState.COLLECTING_COUNTRY,  # Move to next state
                )

                # Create confirmation message
                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                confirmation_message = reply_type.message_text.format(client_name=selected_option.client_name)

                confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
                if confirmed_data.required_fields_except_client_name_are_complete:
                    reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                    confirmation_message = ' '.join((confirmation_message, reply_type.message_text))

                return MessageValidator(
                    conversation_id=conversation_id,
                    role=MessageRole.SYSTEM,
                    type=MessageType.TEXT,
                    content=confirmation_message,
                    selected_option=selected_option,
                    system_reply_type=reply_type,
                )
            else:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=str(ConfirmedDataFields.PROPOSED_CLIENT_NAME),
                    field_value=selected_option.client_name,
                    state=ConversationState.COLLECTING_CLIENT_NAME,  # Move to next state
                )
                suggested_prompts = [SuggestedUserPrompt.YES, SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME]
                return MessageValidator(
                    conversation_id=conversation_id,
                    role=MessageRole.SYSTEM,
                    type=MessageType.TEXT,
                    content=missing_fields.system_message or '',
                    selected_option=selected_option,
                    system_reply_type=missing_fields.system_reply_type,
                    options=[],
                    suggested_prompts=[str(s) for s in suggested_prompts],
                )

        except Exception as e:  # pragma: no cover
            logger.error('Error handling client name selection: %s', e)
            raise
