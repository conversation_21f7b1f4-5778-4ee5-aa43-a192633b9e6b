import json
import logging
from typing import Dict

from cachetools import T<PERSON><PERSON>ache

from repositories import BlobStorageRepository, RoleRepository
from schemas import ProjectRolesData


__all__ = ['RoleDataService']

logger = logging.getLogger(__name__)


class RoleDataService:
    """Service for project roles operations."""

    _cache: TTLCache = TTLCache(maxsize=1, ttl=60 * 60 * 1)  # Cache for 1 hour
    _CACHE_KEY = 'project_roles'

    def __init__(self, role_repository: RoleRepository, blob_repository: BlobStorageRepository):
        self.role_repository = role_repository
        self.blob_repository = blob_repository

    async def list(self, token: str) -> Dict[str, ProjectRolesData]:
        """
        Get cached project roles.

        Returns:
            List of project roles.

        Raises:
            Exception: If an error occurs while getting cached project roles.
        """
        try:
            logger.debug('Getting cached project roles')
            roles = self._cache.get(self._CACHE_KEY)
            if roles is not None:
                logger.debug('Returning project roles from cache')
                return roles

            project_roles = await self.role_repository.list(token)
            processed_project_roles = {role.title: role for role in project_roles}
            self._cache[self._CACHE_KEY] = processed_project_roles

            logger.debug('Uploading project roles to blob storage')
            await self.blob_repository.upload(
                'project-roles.json',
                json.dumps(
                    {
                        'project_roles': {
                            project_role.title: project_role.model_dump()
                            for project_role in processed_project_roles.values()
                        }
                    }
                ).encode('utf-8'),
                'application/json',
            )

            logger.debug('Project roles cached')
            return self._cache[self._CACHE_KEY]
        except Exception as e:
            logger.error('Error listing roles: %s', e)
            raise
