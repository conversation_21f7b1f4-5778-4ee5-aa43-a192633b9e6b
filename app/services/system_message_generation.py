from datetime import date, datetime
import logging
from typing import Sequence

from constants.extracted_data import ConversationState, MissingDataStatus, RequiredField
from constants.message import (
    THANX_FOR_INFORMATION,
    SystemReplyType,
)
from models import QualConversation
from schemas import (
    AggregatedData,
    ClientNameOption,
    ConfirmedData,
    ConversationData,
    DatePickerOption,
    LDMFCountryOption,
    Option,
)
from schemas.extracted_data import MissingDataResponse


__all__ = ['SystemMessageGenerationService']


logger = logging.getLogger(__name__)

GENERATED_MESSAGE = tuple[str, SystemReplyType]


class GeneratedMessage:
    reply_type: SystemReplyType
    content: str
    source: str


class SystemMessageGenerationService:
    """
    Service for generating system messages based on extracted data.

    This service consolidates the system message generation logic from Azure Durable Functions
    into the main FastAPI application for better maintainability and immediate response generation.
    """

    def generate_system_message(
        self,
        conversation_data: ConversationData,
        missing_data_response: MissingDataResponse,
    ) -> GENERATED_MESSAGE:
        """
        Generate a user-friendly system message based on aggregated and confirmed data.

        Args:
            aggregated_data: The aggregated data from all sources
            confirmed_data: The confirmed data from previous user selections
            missing_data_response: Missing data response containing conversation state

        Returns:
            System reply depending on aggregated and confirmed data
        """
        aggregated_data: AggregatedData = conversation_data.aggregated_data
        confirmed_data: ConfirmedData = conversation_data.confirmed_data
        conversation: QualConversation = conversation_data.conversation

        if missing_data_response.status == MissingDataStatus.CLIENT_NAME_NOT_IN_API:
            reply_type = SystemReplyType.CLIENT_NOT_FOUND
            logger.info('%s system_reply_type detected in %s', reply_type, self.generate_system_message.__name__)
            return reply_type.message_text.format(client_name=aggregated_data.client_name[0]), reply_type

        # Early return for collecting dates state
        if str(conversation.State) == str(ConversationState.COLLECTING_DATES):
            reply_type = (
                SystemReplyType.DATES_UNAMBIGUOUS
                if aggregated_data.is_date_unambiguous_and_complete
                else SystemReplyType.DATES_AMBIGUOUS
            )
            logger.info('%s system_reply_type detected in %s', reply_type, self.generate_system_message.__name__)
            return reply_type.message_text, reply_type

        # Early return for initial state
        if str(conversation.State) != str(ConversationState.DATA_COMPLETE) and (
            aggregated_data.all_fields_none or not aggregated_data.client_name
        ):
            reply_type = SystemReplyType.NEED_INFO_INITIAL
            logger.info('%s system_reply_type detected in %s', reply_type, self.generate_system_message.__name__)
            return reply_type.message_text, reply_type

        # Define field processing order and their handlers
        field_handlers = [
            self.generate_for_client_name,
            self.generate_for_ldmf_country,
            self.generate_for_date_intervals,
            self.generate_for_objective_and_scope,
            self.generate_for_outcomes,
        ]

        # Process fields in order, returning first incomplete field's message
        for handler in field_handlers:
            message = handler(aggregated_data, confirmed_data)
            if message:
                logger.info(
                    '%s system_reply_type detected in %s', message[-1], 'generate_system_message:missing_field_handled'
                )
                return message

        return self._generate_confirmation_message_reply(confirmed_data)

    def generate_for_client_name(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> GENERATED_MESSAGE | None:
        """Handle client name field processing."""
        if confirmed_data.client_name:
            return None

        if len(aggregated_data.client_name) == 1:
            return SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION.message_text.format(
                client_name=aggregated_data.client_name[0]
            ), SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION
        else:
            reply_type = SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS
            logger.info('%s system_reply_type detected in %s', reply_type, self.generate_for_client_name.__name__)
            return reply_type.message_text, reply_type

    def generate_for_ldmf_country(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> GENERATED_MESSAGE | None:
        """Handle LDMF country field processing."""
        if confirmed_data.ldmf_country:
            return None

        if not aggregated_data.ldmf_country:
            return SystemReplyType.EXTRACTED_LDMF_NOT_VALID.message_text, SystemReplyType.EXTRACTED_LDMF_NOT_VALID

        if len(aggregated_data.ldmf_country) == 1:
            reply_type = SystemReplyType.LDMF_COUNTRY_SINGLE_CONFIRMATION
            logger.info('%s system_reply_type detected in %s', reply_type, self.generate_for_ldmf_country.__name__)
            return reply_type.message_text.format(ldmf_country=aggregated_data.ldmf_country[0]), reply_type
        else:
            reply_type = SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS
            logger.info('%s system_reply_type detected in %s', reply_type, self.generate_for_ldmf_country.__name__)
            return reply_type.message_text, reply_type

    def generate_for_date_intervals(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> GENERATED_MESSAGE | None:
        """Handle date intervals field processing."""
        if not aggregated_data.date_intervals and not confirmed_data.date_intervals:
            reply_type = SystemReplyType.EXTRACTED_DATA_DATE_INTERVAL_MISSING
            logger.info('%s system_reply_type detected in %s', reply_type, self.generate_for_date_intervals.__name__)
            return reply_type.message_text, reply_type

        if confirmed_data.date_intervals:
            return None

        if not aggregated_data.is_date_unambiguous_and_complete:
            return SystemReplyType.DATES_AMBIGUOUS.message_text, SystemReplyType.DATES_AMBIGUOUS

        return None

    def generate_for_objective_and_scope(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> GENERATED_MESSAGE | None:
        """Handle objective and scope field processing."""

        if confirmed_data.objective_and_scope:
            return None

        if not aggregated_data.objective_and_scope:
            reply_type = SystemReplyType.EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING
            return reply_type.message_text, reply_type

        reply_type = SystemReplyType.CONFIRM_OBJECTIVE_AND_SCOPE
        logger.info('%s system_reply_type detected in %s', reply_type, self.generate_for_objective_and_scope.__name__)
        return reply_type.message_text.format(objective_and_scope=aggregated_data.objective_and_scope), reply_type

    def generate_for_outcomes(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> GENERATED_MESSAGE | None:
        """Handle outcomes field processing."""

        if confirmed_data.outcomes:
            return None

        if not aggregated_data.outcomes:
            reply_type = SystemReplyType.EXTRACTED_DATA_OUTCOMES_MISSING
            logger.info('%s system_reply_type detected in %s', reply_type, self.generate_for_outcomes.__name__)
            return reply_type.message_text, reply_type

        reply_type = SystemReplyType.OUTCOMES_AGGREGATED_QUESTION
        logger.info('%s system_reply_type detected in %s', reply_type, self.generate_for_outcomes.__name__)
        return reply_type.message_text.format(outcomes=aggregated_data.outcomes), reply_type

    def generate_options(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData | None = None,
        conversation_state: ConversationState | None = None,
    ) -> Sequence[Option]:
        """
        Generate user selection options based on aggregated data and confirmed data.

        Args:
            aggregated_data: The aggregated data from all sources
            confirmed_data: The confirmed data from previous user selections

        Returns:
            List of option dictionaries for user selection
        """
        options = []
        if aggregated_data.all_fields_none:
            return options

        # Client Names - highest priority (only if not confirmed)
        if (
            aggregated_data.client_name
            and len(aggregated_data.client_name) > 1
            and (not confirmed_data or not confirmed_data.client_name)
            and conversation_state == ConversationState.COLLECTING_CLIENT_NAME
        ):
            options.extend([ClientNameOption(client_name=name) for name in aggregated_data.client_name])
            return options

        # Lead Member Firm Countries - second priority (only if not confirmed)
        if (
            aggregated_data.ldmf_country
            and len(aggregated_data.ldmf_country) > 1
            and (not confirmed_data or not confirmed_data.ldmf_country)
            and conversation_state == ConversationState.COLLECTING_COUNTRY
        ):
            options.extend([LDMFCountryOption(ldmf_country=country) for country in aggregated_data.ldmf_country])
            return options

        # Engagement Dates - third priority
        if conversation_state == ConversationState.COLLECTING_DATES:
            for start_date_str, end_date_str in aggregated_data.date_intervals:
                if start_date_str or end_date_str:
                    start_date = self._parse_date_string(start_date_str) if start_date_str else None
                    end_date = self._parse_date_string(end_date_str) if end_date_str else None
                    options.append(DatePickerOption(start_date=start_date, end_date=end_date))
                    return options

        return options

    @staticmethod
    def _parse_date_string(date_str: str) -> date | None:
        """
        Parse date string to date object.

        Args:
            date_str: Date string in ISO format (YYYY-MM-DD)

        Returns:
            Date object or None if parsing fails
        """
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except (ValueError, TypeError) as e:
            logger.warning('Failed to parse date string "%s": %s', date_str, e)
            return None

    @staticmethod
    def _generate_confirmation_message_reply(confirmed_data: ConfirmedData) -> GENERATED_MESSAGE:
        """
        Generate system message for user data confirmation.

        Args:
            confirmed_data: The confirmed data from previous user selections

        Returns:
            System reply depending on confirmed data
        """
        reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
        system_message = f'{THANX_FOR_INFORMATION} {reply_type.message_text}'

        if confirmed_data.last_confirmed_field == RequiredField.CLIENT_NAME:
            reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
            system_message = reply_type.message_text.format(client_name=confirmed_data.client_name)

        elif confirmed_data.last_confirmed_field == RequiredField.LDMF_COUNTRY:
            reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
            system_message = reply_type.message_text.format(ldmf_country=confirmed_data.ldmf_country)

        if confirmed_data.last_confirmed_field in (RequiredField.CLIENT_NAME, RequiredField.LDMF_COUNTRY):
            if confirmed_data.required_fields_are_complete:
                reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                system_message = f'{system_message} {reply_type.message_text}'

        logger.info(
            '%s system_reply_type detected in %s',
            reply_type,
            'SystemMessageGenerationService._generate_confirmation_message_reply',
        )
        return system_message, reply_type
