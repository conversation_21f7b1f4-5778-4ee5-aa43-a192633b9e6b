import logging
from typing import Any

from constants.extracted_data import FieldStatus, RequiredField
from constants.message import SystemReplyType
from schemas import AggregatedData, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData

from .base import BaseFieldHandler


__all__ = ['ObjectiveHandler']
logger = logging.getLogger(__name__)


class ObjectiveHandler(BaseFieldHandler):
    """Handler for RequiredField.OBJECTIVE_SCOPE."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        called_from: str = '',
    ) -> FieldHandlerResponse:
        logger.info('Called ObjectiveHandler.check_and_get_response from %s', called_from)

        # Check if objective_and_scope is already confirmed
        if confirmed_data.objective_and_scope is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                system_reply_type=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if objective_and_scope is in aggregated data
        elif aggregated_data.objective_and_scope is not None:
            reply_type = SystemReplyType.CONFIRM_OBJECTIVE_AND_SCOPE
            system_message = reply_type.message_text.format(objective_and_scope=aggregated_data.objective_and_scope)
            logger.info('%s system_reply_type detected in %s', reply_type, 'ObjectiveHandler.check_and_get_response')
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            reply_type = SystemReplyType.NEED_INFO_OBJECTIVE_SCOPE
            system_message = reply_type.message_text
            logger.info('%s system_reply_type detected in %s', reply_type, 'ObjectiveHandler.check_and_get_response')
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=RequiredField.OBJECTIVE_SCOPE,
            )

    async def validate_value(self, field_value: Any, called_from: str = '') -> FieldHandlerResponse:
        return await self.check_and_get_response(field_value, ConfirmedData(), called_from)
