from datetime import datetime
import logging
from typing import Any, Sequence
from uuid import UUID

from constants.extracted_data import (
    ConversationState,
    DataSourceType,
    MissingDataStatus,
    RequiredField,
    StrategyFactoryKey,
)
from constants.message import SystemReplyType
from repositories import (
    ConversationRepository,
    ExtractedDataRepository,
    QualsClientsRepository,
)
from schemas import (
    AggregatedData,
    ClientAPIParamsItem,
    ClientCreateRequest,
    ClientSearchItem,
    ClientSearchRequest,
    CombinedExtractedDataResponse,
    ConfirmedData,
    ConversationData,
    ConversationResponse,
    ExtractedData,
    MissingDataResponse,
    ProjectRolesData,
)
from schemas.conversation_message.option import LDMFCountryOption
from schemas.extracted_data import (
    EngagementDates,
    EngagementDatesOriginal,
    FieldHandlerResponse,
    IndustryData,
    ServiceData,
)
from schemas.ldmf_countries import CountryData
from services.client_industry import ClientIndustryDataService
from services.fee_and_currency_service import FeeAndCurrencyService
from services.ldmf_country import LDMFCountryService
from services.project_role import RoleDataService
from services.user_info import UserInfoService

from .handlers import (
    BaseFieldHandler,
    ClientNameHandler,
    DateIntervalsHandler,
    LDMFCountryHandler,
    ObjectiveHandler,
    OutcomesHandler,
    TokenRequiredFieldHandler,
)
from .parsers import KXDashDataParser, PromptAndDocumentDataParser
from .strategies import (
    ConcatenationStrategy,
    DateIntervalCollectionStrategy,
    EnhancedDateIntervalCollectionStrategy,
    EnhancedMergeUniqueValuesStrategy,
    EnhancedPriorityOverrideStrategy,
    MergeUniqueValuesStrategy,
    PriorityOverrideStrategy,
    StrategyType,
    TeamRolesAggregationStrategy,
)


__all__ = ['ExtractedDataService']


logger = logging.getLogger(__name__)


CURRENCY_CODE_TO_NAME_MAP = {
    'USD': 'US',
    'EUR': 'EUR',
    'GBP': 'GBP',
    'AUD': 'AUD',
    'JPY': 'Yen',
}


class ExtractedDataService:
    _SOURCE_TYPE_TO_PARSER_MAP = {
        DataSourceType.KX_DASH: KXDashDataParser,
        DataSourceType.DOCUMENTS: PromptAndDocumentDataParser,
        DataSourceType.PROMPT: PromptAndDocumentDataParser,
    }

    _SOURCE_DATA_PROCESSING_PRIORITY = [DataSourceType.KX_DASH, DataSourceType.DOCUMENTS, DataSourceType.PROMPT]

    _FIELD_HANDLERS: dict[RequiredField, BaseFieldHandler | TokenRequiredFieldHandler] = {
        RequiredField.ENGAGEMENT_DATES: DateIntervalsHandler(),
        RequiredField.OBJECTIVE_SCOPE: ObjectiveHandler(),
        RequiredField.OUTCOMES: OutcomesHandler(),
    }

    # Define the order in which fields should be collected
    _FIELD_COLLECTION_ORDER = [
        RequiredField.CLIENT_NAME,
        RequiredField.LDMF_COUNTRY,
        RequiredField.ENGAGEMENT_DATES,
        RequiredField.OBJECTIVE_SCOPE,
        RequiredField.OUTCOMES,
    ]

    # Map fields to conversation states
    _FIELD_TO_STATE_MAP = {
        RequiredField.CLIENT_NAME: ConversationState.COLLECTING_CLIENT_NAME,
        RequiredField.LDMF_COUNTRY: ConversationState.COLLECTING_COUNTRY,
        RequiredField.ENGAGEMENT_DATES: ConversationState.COLLECTING_DATES,
        RequiredField.OBJECTIVE_SCOPE: ConversationState.COLLECTING_OBJECTIVE,
        RequiredField.OUTCOMES: ConversationState.COLLECTING_OUTCOMES,
    }

    _AVERAGE_DAYS_PER_MONTH = 30.44

    _ROLE_LEAD_ENGAGEMENT_PARTNER = 'Lead Engagement Partner'
    _ROLE_LCSP = 'LCSP'
    _ROLE_ENGAGEMENT_MANAGER = 'Engagement Manager'

    def __init__(
        self,
        industry_data_service: ClientIndustryDataService,
        role_data_service: RoleDataService,
        service_data_service: FeeAndCurrencyService,
        user_info_service: UserInfoService,
        extracted_data_repository: ExtractedDataRepository,
        conversation_repository: ConversationRepository,
        quals_clients_repository: QualsClientsRepository,
        ldmf_country_service: LDMFCountryService,
    ):
        self.extracted_data_repository = extracted_data_repository
        self.industry_data_service = industry_data_service
        self.role_data_service = role_data_service
        self.user_info_service = user_info_service
        self.service_data_service = service_data_service
        self.conversation_repository = conversation_repository
        self.quals_clients_repository = quals_clients_repository
        self.ldmf_country_service = ldmf_country_service
        self._FIELD_HANDLERS[RequiredField.CLIENT_NAME] = ClientNameHandler(quals_clients_repository)
        self._FIELD_HANDLERS[RequiredField.LDMF_COUNTRY] = LDMFCountryHandler(ldmf_country_service)

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data from.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Deleting extracted data for conversation ID: %s', conversation_id)
        try:
            await self.extracted_data_repository.delete_many(conversation_id)
        except Exception as e:  # pragma: no cover
            logger.error("Failed to delete extracted data for conversation ID '%s': %s", conversation_id, e)
            raise

    async def _process_activity_data(self, activity_data: dict[str, Any], token: str) -> dict[str, Any]:
        """
        Process the activity data.
        """
        # Initialize default empty dictionaries for external data
        roles: dict[str, ProjectRolesData] = {}

        try:
            roles = await self.role_data_service.list(token)
        except Exception as e:
            logger.warning('Failed to fetch external reference data: %s', e)

        # Process client industry data if available
        industry_name: str | None = activity_data.get('global_industry')

        if industry_name:
            activity_data['client_industries'] = [
                IndustryData(
                    name=industry_name,
                    path=activity_data.get('global_industry_sector') or '',
                    ldmf_country='Global',
                    is_primary=True,
                )
            ]

        # Process service data if available
        service_name: str | None = activity_data.get('global_business_service_line')

        if service_name:
            activity_data['client_services'] = [
                ServiceData(
                    name=service_name,
                    path=activity_data.get('global_business_service_area') or '',
                    is_primary=True,
                )
            ]

        # Process role data if available
        lcsp_emails = activity_data.get('global_lcsp_emails')
        lep_emails = activity_data.get('engagement_lep_emails')
        manager_emails = activity_data.get('engagement_manager_emails')

        if not roles:
            activity_data['team_roles'] = []
            return activity_data

        processed_roles = {}

        if lep_emails and (lep_role := roles.get(self._ROLE_LEAD_ENGAGEMENT_PARTNER)):
            processed_roles = await self._process_roles(
                processed_roles,
                lep_emails,
                lep_role,
                token,
                default_is_approver=True,
            )

        if lcsp_emails and (lcsp_role := roles.get(self._ROLE_LCSP)):
            processed_roles = await self._process_roles(
                processed_roles,
                lcsp_emails,
                lcsp_role,
                token,
                default_is_approver=True,
            )

        if manager_emails and (manager_role := roles.get(self._ROLE_ENGAGEMENT_MANAGER)):
            processed_roles = await self._process_roles(
                processed_roles,
                manager_emails,
                manager_role,
                token,
            )

        activity_data['team_roles'] = list(processed_roles.values())
        return activity_data

    async def _process_roles(
        self,
        processed_roles: dict[str, dict[str, Any]],
        emails: list[str],
        role: ProjectRolesData,
        token: str,
        default_is_approver: bool = False,
    ) -> dict[str, dict[str, Any]]:
        """
        Process roles for a given role id.
        """
        processed_role = role.model_dump()
        for email in emails:
            if email in processed_roles:
                processed_roles[email]['roles'].append(processed_role)
                continue

            if '@' not in email:
                logger.warning(f'Invalid email format: {email}')
                continue

            deloitte_id = email.split('@')[0]
            try:
                user_info = await self.user_info_service.get(deloitte_id, token)
            except Exception as e:
                logger.warning(f'Failed to fetch user info for {email}: {e}')
                user_info = {}

            value = {
                'email': email,
                'roles': [processed_role],
                'name': user_info.get('name'),
                'is_approver': user_info.get('isApprover') if not default_is_approver else default_is_approver,
                'is_contact': user_info.get('isContact', False),
            }
            processed_roles[email] = value

        return processed_roles

    async def update(
        self, conversation_id: UUID, raw_data: dict[str, Any], source_type: DataSourceType, token: str
    ) -> ExtractedData:
        """
        Update extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to update extracted data for.
            raw_data: The data to update the extracted data with.
            source_type: The type of data source being updated.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
            ServiceExceptionError: If the extracted data record is not found.
        """
        logger.debug('Started updating "%s" extracted data for conversation ID "%s"', source_type, conversation_id)
        try:
            extracted_data = await self.extracted_data_repository.get(
                conversation_id=conversation_id,
                data_source_type=source_type,
            ) or ExtractedData.create(conversation_id=conversation_id, data_source_type=source_type)
            processed_activity_data = await self._process_activity_data(raw_data, token)
            parser = self._SOURCE_TYPE_TO_PARSER_MAP[source_type]()
            await self.extracted_data_repository.update(parser(extracted_data, processed_activity_data))
            return extracted_data

        except Exception as e:  # pragma: no cover
            logger.error(
                'Failed to update "%s" extracted data for conversation ID "%s": %s', source_type, conversation_id, e
            )
            raise

    def _apply_aggregation_strategies(
        self, extracted_data: ExtractedData, strategies: dict[StrategyFactoryKey, StrategyType]
    ) -> None:
        """Apply all aggregation strategies to the extracted data."""
        strategies[StrategyFactoryKey.MERGE].process(extracted_data)
        strategies[StrategyFactoryKey.DATE].process(extracted_data)
        strategies[StrategyFactoryKey.PRIORITY].process(extracted_data)
        strategies[StrategyFactoryKey.CONCATENATION].process(extracted_data)
        strategies[StrategyFactoryKey.ENHANCED_MERGE].process(extracted_data)
        strategies[StrategyFactoryKey.ENHANCED_PRIORITY].process(extracted_data)
        strategies[StrategyFactoryKey.ENHANCED_DATE].process(extracted_data)
        strategies[StrategyFactoryKey.TEAM_ROLES].process(extracted_data)

    def _initialize_aggregation_strategies(self) -> dict[StrategyFactoryKey, StrategyType]:
        """Initialize all aggregation strategies."""
        return {
            StrategyFactoryKey.MERGE: MergeUniqueValuesStrategy(),
            StrategyFactoryKey.DATE: DateIntervalCollectionStrategy(),
            StrategyFactoryKey.PRIORITY: PriorityOverrideStrategy(),
            StrategyFactoryKey.CONCATENATION: ConcatenationStrategy(),
            StrategyFactoryKey.ENHANCED_MERGE: EnhancedMergeUniqueValuesStrategy(),
            StrategyFactoryKey.ENHANCED_PRIORITY: EnhancedPriorityOverrideStrategy(),
            StrategyFactoryKey.ENHANCED_DATE: EnhancedDateIntervalCollectionStrategy(),
            StrategyFactoryKey.TEAM_ROLES: TeamRolesAggregationStrategy(),
        }

    def _create_aggregated_data_model(
        self, strategies: dict[StrategyFactoryKey, Any], project_duration: int
    ) -> AggregatedData:
        """Create the AggregatedData model from the strategies."""
        merge_strategy = strategies[StrategyFactoryKey.MERGE]
        date_strategy = strategies[StrategyFactoryKey.DATE]
        priority_strategy = strategies[StrategyFactoryKey.PRIORITY]
        concatenation_strategy = strategies[StrategyFactoryKey.CONCATENATION]
        enhanced_merge_strategy = strategies[StrategyFactoryKey.ENHANCED_MERGE]
        enhanced_priority_strategy = strategies[StrategyFactoryKey.ENHANCED_PRIORITY]
        enhanced_date_strategy = strategies[StrategyFactoryKey.ENHANCED_DATE]
        team_roles_strategy = strategies[StrategyFactoryKey.TEAM_ROLES]

        team_roles = team_roles_strategy.get_team_roles_json()

        # Ensure team_roles is always a list for validation
        if team_roles is not None:
            for user in team_roles:
                if not user.get('duration'):
                    user['duration'] = project_duration
        else:
            team_roles = []

        return AggregatedData(
            # Core fields (existing)
            client_name=merge_strategy.get_client_names(),
            ldmf_country=merge_strategy.get_ldmf_countries(),
            title=merge_strategy.get_titles(),
            date_intervals=date_strategy.get_date_intervals(),
            date_intervals_original=date_strategy.get_date_intervals_original(),
            more_than_two_dates=date_strategy.get_more_than_two_dates(),
            objective_and_scope=priority_strategy.get_objective_and_scope(),
            outcomes=priority_strategy.get_outcomes(),
            # Engagement Description fields
            business_issues=concatenation_strategy.get_business_issues(),
            scope_approach=concatenation_strategy.get_scope_approach(),
            value_delivered=concatenation_strategy.get_value_delivered(),
            engagement_summary=concatenation_strategy.get_engagement_summary(),
            one_line_description=concatenation_strategy.get_one_line_description(),
            # Engagement Details fields
            client_references=concatenation_strategy.get_client_references(),
            client_name_sharing=concatenation_strategy.get_client_name_sharing(),
            client_industries=enhanced_merge_strategy.get_client_industries(),
            engagement_dates=enhanced_date_strategy.get_engagement_dates(),
            engagement_locations=enhanced_merge_strategy.get_engagement_locations(),
            engagement_fee=enhanced_priority_strategy.get_engagement_fee(),
            engagement_fee_currency=enhanced_priority_strategy.get_engagement_fee_currency(),
            is_engagement_fee_unknown=enhanced_priority_strategy.get_is_engagement_fee_unknown(),
            is_pro_bono=enhanced_priority_strategy.get_is_pro_bono(),
            engagement_fee_display=enhanced_priority_strategy.get_engagement_fee_display(),
            client_services=enhanced_merge_strategy.get_client_services(),
            source_of_work=priority_strategy.get_source_of_work(),
            # Usage & Team fields
            qual_usage=concatenation_strategy.get_qual_usage(),
            team_roles=team_roles,
            approver=concatenation_strategy.get_approver(),
        )

    async def _calculate_project_duration(self, conversation_id: UUID) -> int:
        """Calculate the duration of the project in months."""
        confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
        if not confirmed_data.date_intervals:
            return 0

        start_date, end_date = confirmed_data.date_intervals
        if not start_date or not end_date:
            return 0

        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError as e:
            logger.warning(f'Invalid date format for conversation {conversation_id}: {e}')
            return 0

        if start_date_obj > end_date_obj:
            logger.warning(f'Start date after end date for conversation {conversation_id}')
            return 0

        duration_delta = end_date_obj - start_date_obj
        return max(0, round(duration_delta.days / self._AVERAGE_DAYS_PER_MONTH))  # Average days per month

    async def aggregate_data(self, conversation_id: UUID) -> AggregatedData:
        """
        Aggregate data from all sources for a conversation, applying different strategies:
        - client_name, ldmf_country, title: Merge unique values from all sources
        - date_intervals: Collect date ranges as (start_date, end_date) pairs from all sources
        - objective_and_scope, outcomes: Priority-based override (highest priority wins)

        Args:
            conversation_id: The ID of the conversation to aggregate data for.

        Returns:
            AggregatedData: The aggregated data from all sources.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        strategies = self._initialize_aggregation_strategies()
        duration = await self._calculate_project_duration(conversation_id)

        # Process data sources in priority order (lowest to highest)
        for source_type in self._SOURCE_DATA_PROCESSING_PRIORITY:
            try:
                extracted_data = await self.extracted_data_repository.get(
                    conversation_id=conversation_id,
                    data_source_type=source_type,
                )

                if not extracted_data:
                    logger.debug('No %s data found for conversation %s', source_type, conversation_id)
                    continue

                self._apply_aggregation_strategies(extracted_data, strategies)

            except Exception as e:
                logger.warning('Error processing %s data for conversation %s: %s', source_type, conversation_id, e)
                continue

        return self._create_aggregated_data_model(strategies, duration)

    async def get_missing_required_data_prompts(
        self,
        conversation_id: UUID,
        token: str,
        confirmed_data: ConfirmedData | None = None,
        called_from: str = '',
        conversation_data: ConversationData | None = None,
    ) -> MissingDataResponse:
        """
        Method for progressive data collection with user confirmation tracking.

        Args:
            conversation_id: The ID of the conversation to check.
            confirmed_data: User-confirmed data from previous interactions.

        Returns:
            MissingDataResponse: Structured response with next steps for data collection.
        """
        logger.info(
            'Enhanced missing data check for conversation ID: %s was called from %s', conversation_id, called_from
        )

        if confirmed_data is None:
            confirmed_data = ConfirmedData()

        try:
            aggregated_data = await self.aggregate_data(conversation_id)
            missing_fields = []
            # If none of the fields were provided, there is no need to call handlers for each field separately. We should just return initial reply.
            if (
                conversation_data is not None
                and str(conversation_data.conversation.State)
                in [
                    ConversationState.INITIAL,
                    ConversationState.COLLECTING_CLIENT_NAME,
                ]
                and aggregated_data.all_fields_none
                and confirmed_data.all_fields_none
            ):
                reply_type = SystemReplyType.NEED_INFO_INITIAL_MISSING
                return MissingDataResponse(
                    status=MissingDataStatus.MISSING_DATA,
                    reply_type=reply_type,
                    message=reply_type.message_text,
                    next_expected_field=None,
                    missing_fields=[field_type.value for field_type in self._FIELD_COLLECTION_ORDER],
                    conversation_state=ConversationState.INITIAL,
                    options=[],
                )

            # Find the first field that needs confirmation or auto-confirmation
            for field_type in self._FIELD_COLLECTION_ORDER:
                handler = self._FIELD_HANDLERS[field_type]
                response = await self._call_handler_check_and_get_response(
                    handler,
                    aggregated_data,
                    confirmed_data,
                    token,
                    called_from='ExtractedDataService.get_missing_required_data_prompts',
                )

                if response.needs_confirmation:
                    missing_fields.append(response.next_expected_field or field_type.value)

                if response.needs_confirmation:
                    if response.system_reply_type == SystemReplyType.EXTRACTED_LDMF_NOT_VALID:
                        return MissingDataResponse(
                            status=MissingDataStatus.EXTRACTED_DATA_NOT_VALID,
                            message=response.system_message,
                            reply_type=response.system_reply_type,
                            next_expected_field=response.next_expected_field,
                            missing_fields=missing_fields,
                            conversation_state=self._FIELD_TO_STATE_MAP[field_type],
                            options=response.options,
                        )
                    if response.system_reply_type == SystemReplyType.CLIENT_NOT_FOUND:
                        return MissingDataResponse(
                            status=MissingDataStatus.CLIENT_NAME_NOT_IN_API,
                            message=response.system_message,
                            reply_type=response.system_reply_type,
                            next_expected_field=response.next_expected_field,
                            missing_fields=missing_fields,
                            conversation_state=self._FIELD_TO_STATE_MAP[field_type],
                            options=[],
                        )
                    return MissingDataResponse(
                        status=MissingDataStatus.MISSING_DATA,
                        message=response.system_message,
                        reply_type=response.system_reply_type,
                        next_expected_field=response.next_expected_field,
                        missing_fields=missing_fields,
                        conversation_state=self._FIELD_TO_STATE_MAP[field_type],
                        options=response.options,
                    )

            # All fields are confirmed
            return MissingDataResponse(
                status=MissingDataStatus.DATA_COMPLETE,
                message=None,
                reply_type=None,
                next_expected_field=None,
                missing_fields=[],
                conversation_state=ConversationState.DATA_COMPLETE,
            )

        except Exception as e:
            logger.error("Failed to check enhanced missing data for conversation ID '%s': %s", conversation_id, e)
            return MissingDataResponse(
                status=MissingDataStatus.ERROR,
                message='An error occurred while checking data completeness.',
                reply_type=None,
                next_expected_field=None,
                missing_fields=[],
                conversation_state=ConversationState.INITIAL,
            )

    async def delete(self, conversation_id: UUID, data_source_type: DataSourceType) -> None:
        """
        Delete extracted data of a specified source type for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data for.
            data_source_type: The type of data source to delete extracted data for.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Deleting extracted data for conversation ID: %s', conversation_id)
        try:
            await self.extracted_data_repository.delete(conversation_id, data_source_type)

        except Exception as e:
            logger.error("Failed to delete extracted data for conversation ID '%s': %s", conversation_id, e)
            raise

    async def update_confirmed_data(
        self,
        conversation_id: UUID,
        field_name: str,
        field_value: str | Sequence[str | None],
        state: ConversationState,
    ) -> None:
        """
        Update confirmed data for a specific field and update conversation state.

        Args:
            conversation_id: The ID of the conversation to update
            field_name: The name of the field to update (e.g., 'client_name')
            field_value: The confirmed value for the field
            state: The new conversation state

        Raises:
            EntityNotFoundError: If the conversation does not exist

        Re-raises:
            ConfirmedDataReplacementError: If the confirmed data is being replaced by update_confirmed_data_and_state
        """
        logger.debug('Updating confirmed data for conversation ID: %s, field: %s', conversation_id, field_name)

        try:
            # Get current confirmed data
            current_confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)

            # Update the specific field
            updated_data = current_confirmed_data.model_copy()
            setattr(updated_data, field_name, field_value)
            # Track which field was just confirmed
            updated_data.last_confirmed_field = field_name

            # Determine the next conversation state based on the updated confirmed data
            next_state = updated_data.get_current_conversation_state()

            # Update both confirmed data and state
            await self.conversation_repository.update_confirmed_data_and_state(
                public_id=conversation_id,
                confirmed_data=updated_data,
                state=next_state,
            )

            logger.debug('Successfully updated confirmed data for conversation ID: %s', conversation_id)

        except Exception as e:
            logger.error('Failed to update confirmed data for conversation ID: %s: %s', conversation_id, e)
            raise

    async def _call_handler_check_and_get_response(
        self,
        handler: BaseFieldHandler | TokenRequiredFieldHandler,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        token: str,
        called_from: str = '',
    ) -> FieldHandlerResponse:
        # Call the appropriate method based on handler type
        if isinstance(handler, TokenRequiredFieldHandler):
            response = await handler.check_and_get_response(aggregated_data, confirmed_data, token, called_from)
        else:
            response = await handler.check_and_get_response(aggregated_data, confirmed_data, called_from)

        return response

    async def _call_handler_to_validate_value(
        self, handler: BaseFieldHandler | TokenRequiredFieldHandler, token: str, field_value: Any, called_from: str = ''
    ) -> FieldHandlerResponse:
        if isinstance(handler, TokenRequiredFieldHandler):
            response = await handler.validate_value(token, field_value, called_from)
        else:
            response = await handler.validate_value(field_value, called_from)

        return response

    def catch_invalid_ldmf_option(
        self,
        missing_data_response: MissingDataResponse,
        conversation_state: ConversationState,
        system_reply_type: SystemReplyType | None,
    ) -> LDMFCountryOption | None:
        """
        Catch invalid LDMF country option and return a LDMFCountryOption with an empty string.

        Args:
            missing_data_response: The missing data response
            conversation_state: The current conversation state

        Returns:
            LDMFCountryOption: The LDMFCountryOption with an empty string if the conversation state is COLLECTING_COUNTRY and the missing data response status is EXTRACTED_DATA_NOT_VALID, None otherwise.
        """
        if (
            missing_data_response.status
            in [
                MissingDataStatus.EXTRACTED_DATA_NOT_VALID,
                MissingDataStatus.USER_INSERTS_LDMF,
                MissingDataStatus.MISSING_DATA,
            ]
            and conversation_state == ConversationState.COLLECTING_COUNTRY
        ):
            return LDMFCountryOption(ldmf_country='')
        if system_reply_type and system_reply_type == SystemReplyType.EXTRACTED_LDMF_NOT_VALID:
            return LDMFCountryOption(ldmf_country='')
        return None

    async def single_value_validation(
        self,
        field: RequiredField,
        field_value: Any,
        token: str,
        called_from: str = '',
    ) -> FieldHandlerResponse:
        """
        Validate a single value for a field.

        Args:
            conversation_id: The conversation ID
            field_name: The name of the field to validate
            field_value: The value to validate
            token: The user's token

        Returns:
            True if the value is valid, False otherwise
        """
        try:
            handler = self._FIELD_HANDLERS[field]
            response = await self._call_handler_to_validate_value(handler, token, field_value, called_from)
            return response
        except Exception:
            logger.exception('Failed to validate %s', field)
            raise

    async def get_or_create_client(self, client_name: str, token: str) -> bool:
        if not client_name or not client_name.strip():
            logger.warning('Invalid client name provided: %s', client_name)
            return False
        search_request = ClientSearchRequest(contains=client_name, page_size=50, page_idx=0)
        search_result = await self.quals_clients_repository.search_clients(search_request, token)
        is_found = client_name in [client.name for client in search_result.clients]
        if is_found:
            return False
        request = ClientCreateRequest(name=client_name)
        client = await self.quals_clients_repository.create_client(request, token)
        return client.success

    async def update_date_intervals(
        self,
        conversation_id: UUID,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        dates_analyzed: EngagementDates,
        dates_original: EngagementDatesOriginal,
        token: str,
    ) -> tuple[str | None, str | None]:
        analyzed_start_date = dates_analyzed.start_date.isoformat() if dates_analyzed.start_date else None
        analyzed_end_date = dates_analyzed.end_date.isoformat() if dates_analyzed.end_date else None
        if confirmed_data.date_intervals:
            start_date, end_date = confirmed_data.date_intervals
            if analyzed_start_date or analyzed_end_date:
                start_date = analyzed_start_date or start_date
                end_date = analyzed_end_date or end_date
        else:
            extracted_start_date, extracted_end_date = None, None
            if aggregated_data.date_intervals:
                extracted_start_date, extracted_end_date = aggregated_data.date_intervals[-1]

            start_date = analyzed_start_date or extracted_start_date
            end_date = analyzed_end_date or extracted_end_date

            updated_dates = {
                'start_date': start_date,
                'end_date': end_date,
                'start_date_original': dates_original.start_date,
                'end_date_original': dates_original.end_date,
            }
            await self.update(conversation_id, updated_dates, DataSourceType.PROMPT, token)

        return start_date, end_date

    async def get_combined_extracted_data(
        self, conversation: ConversationResponse, confirmed_data: ConfirmedData, token: str
    )-> CombinedExtractedDataResponse:
        aggregated_data = await self.aggregate_data(conversation.id)

        ldmf_country = None
        if confirmed_data.ldmf_country:
            ldmf_country = await self._fetch_ldmf_country(confirmed_data.ldmf_country, token)

        fetched_client_name = None
        client_confidentiality = None
        if confirmed_data.client_name:
            fetched_client_name = await self._fetch_exact_client_name(confirmed_data.client_name, token)
            if fetched_client_name:
                client_confidentiality = await self.quals_clients_repository.get_client_confidentiality_list(token)

        client_references = await self._fetch_client_references(aggregated_data.client_references, token)
        client_name_sharing = await self._fetch_client_name_sharing(aggregated_data.client_name_sharing, token)

        fee_display_options = await self.service_data_service.get_project_fee_display_options(token)
        currencies = await self.service_data_service.get_currencies(token)

        engagement_fee_display_obj = None
        if aggregated_data.engagement_fee_display:
            for option in fee_display_options:
                if option.name == aggregated_data.engagement_fee_display:
                    engagement_fee_display_obj = option
                    break

        engagement_fee_currency_obj = None
        if aggregated_data.engagement_fee_currency:
            currency_name_to_find = CURRENCY_CODE_TO_NAME_MAP.get(
                aggregated_data.engagement_fee_currency,
                aggregated_data.engagement_fee_currency,
            )
            for currency in currencies:
                if currency.name == currency_name_to_find:
                    engagement_fee_currency_obj = currency
                    break

        # If no industry is marked as primary, mark the first one as primary
        if aggregated_data.client_industries and not any(
            industry.is_primary for industry in aggregated_data.client_industries
        ):
            aggregated_data.client_industries[0].is_primary = True

        # If no service is marked as primary, mark the first one as primary
        if aggregated_data.client_services and not any(
            service.is_primary for service in aggregated_data.client_services
        ):
            aggregated_data.client_services[0].is_primary = True

        response = await CombinedExtractedDataResponse.from_confirmed_and_aggregated_data(
            conversation_id=conversation.id,
            confirmed_data=confirmed_data,
            aggregated_data=aggregated_data,
            dash_activity_id=conversation.dash_activity_id,
            ldmf_country=ldmf_country,
            fetched_client_name=fetched_client_name,
            client_references=client_references,
            client_name_sharing=client_name_sharing,
            client_confidentiality=client_confidentiality,
            engagement_fee_display=engagement_fee_display_obj,
            engagement_fee_currency=engagement_fee_currency_obj,
        )
        return await self._mask_client_name_in_response(response, confirmed_data.client_name or '')

    async def _fetch_ldmf_country(self, ldmf_country: str, token: str) -> CountryData | None:
        """
        Fetches the exact LDMF country from the LDMF Countries API.

        Args:
            ldmf_country: The LDMF country to search for.
            token: Bearer token for authentication.

        Returns:
            Optional[str]: The exact LDMF country if found, otherwise None.
        """
        if not ldmf_country:
            return None

        countries_dict = await self.ldmf_country_service.list(token)
        ldmf_countries_list = list(countries_dict.values())
        for country in ldmf_countries_list:
            if country.name == ldmf_country:
                return country
        return None

    async def _fetch_exact_client_name(self, client_name: str, token: str) -> ClientSearchItem | None:
        """
        Fetches the exact client name from the Quals Clients API.

        Args:
            client_name: The client name to search for.
            token: Bearer token for authentication.

        Returns:
            Optional[str]: The exact client name if found, otherwise None.
        """
        if not client_name:
            return None

        search_request = ClientSearchRequest(contains=client_name, page_size=20, page_idx=0)
        search_response = await self.quals_clients_repository.search_clients(search_request, token)

        if search_response.exact_match and search_response.clients:
            for client in search_response.clients:
                if client.name == client_name:
                    return client
        return None

    async def _fetch_client_references(self, client_references, token) -> ClientAPIParamsItem | None:
        """
        Map client references name to ClientAPIParamsItem object based on predefined mapping.
        """
        CLIENT_REFERENCES_NAME_MAPPING = {
            'true': 'Yes',
            'false': 'No',
            'sometimes': 'In some cases',
            'undefined': "I don't know",
        }
        client_references_list = await self.quals_clients_repository.get_references_list(token)

        for item in client_references_list:
            if item.name == CLIENT_REFERENCES_NAME_MAPPING.get(client_references):
                return item
        return None

    async def _fetch_client_name_sharing(self, client_sharing, token) -> ClientAPIParamsItem | None:
        """
        Fetches the client name sharing setting from the Quals Clients API.

        Returns:
            bool: True if client name sharing is enabled, False otherwise.
        """
        CLIENT_SHARING_NAME_MAPPING = {
            'true': 'Client name may be shared internally',
            'false': 'Client name may NOT be shared internally',
        }
        client_sharing_list = await self.quals_clients_repository.get_client_sharing_list(token)

        for item in client_sharing_list:
            if item.name == CLIENT_SHARING_NAME_MAPPING.get(client_sharing):
                return item
        return None

    async def _mask_client_name_in_response(
        self, response: CombinedExtractedDataResponse, client_name: str
    ) -> CombinedExtractedDataResponse:
        """
        Mask the client name in the response.
        """
        if not client_name:
            return response

        fields_to_mask = [
            'business_issues',
            'scope_approach',
            'value_delivered',
            'engagement_summary',
            'one_line_description',
            'title',
        ]

        for field in fields_to_mask:
            field_value = getattr(response, field)
            if isinstance(field_value, str):
                setattr(response, field, field_value.replace(client_name, 'the client'))

        if response.conversation_id:
            await self.extracted_data_repository.update_masked_data(response.conversation_id, response)

        return response
