"""Enhanced priority override strategy for additional fields."""

from schemas import ExtractedData

from .base import BaseAggregationStrategy


class EnhancedPriorityOverrideStrategy(BaseAggregationStrategy):
    """Strategy for priority override behavior where latest value wins for additional fields."""

    def __init__(self):
        # Existing fields
        self._objective_and_scope: str | None = None
        self._outcomes: str | None = None

        # Engagement Fee fields
        self._engagement_fee: float | None = None
        self._engagement_fee_currency: str | None = None
        self._is_engagement_fee_unknown: bool | None = None
        self._is_pro_bono: bool | None = None
        self._engagement_fee_display: str | None = None

    def process(self, extracted_data: ExtractedData) -> None:
        """Apply priority override for specified fields (latest value wins)."""
        # Existing fields
        if extracted_data.objective_and_scope:
            self._objective_and_scope = extracted_data.objective_and_scope
        if extracted_data.outcomes:
            self._outcomes = extracted_data.outcomes

        # Engagement Fee fields (block override)
        if self._source_has_fee_data(extracted_data):
            self._engagement_fee = extracted_data.engagement_fee
            self._engagement_fee_currency = extracted_data.engagement_fee_currency
            self._is_engagement_fee_unknown = extracted_data.is_engagement_fee_unknown
            self._is_pro_bono = extracted_data.is_pro_bono
            self._engagement_fee_display = extracted_data.engagement_fee_display

    def _source_has_fee_data(self, extracted_data: ExtractedData) -> bool:
        """Check if the extracted data contains any engagement fee related information."""
        fee_fields = [
            extracted_data.engagement_fee,
            extracted_data.engagement_fee_currency,
            extracted_data.is_engagement_fee_unknown,
            extracted_data.is_pro_bono,
            extracted_data.engagement_fee_display,
        ]
        return any(field is not None for field in fee_fields)

    def get_objective_and_scope(self) -> str | None:
        """Get objective and scope (latest value)."""
        return self._objective_and_scope

    def get_outcomes(self) -> str | None:
        """Get outcomes (latest value)."""
        return self._outcomes

    def get_engagement_fee(self) -> float | None:
        return self._engagement_fee

    def get_engagement_fee_currency(self) -> str | None:
        return self._engagement_fee_currency

    def get_is_engagement_fee_unknown(self) -> bool | None:
        return self._is_engagement_fee_unknown

    def get_is_pro_bono(self) -> bool | None:
        return self._is_pro_bono

    def get_engagement_fee_display(self) -> str | None:
        """Get engagement fee display (latest value)."""
        return self._engagement_fee_display
