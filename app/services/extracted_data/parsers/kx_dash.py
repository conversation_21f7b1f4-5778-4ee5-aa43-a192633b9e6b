from datetime import date
import json
import logging
from typing import Any

from schemas import ExtractedData


__all__ = ['KXDashDataParser']


logger = logging.getLogger(__name__)


class KXDashDataParser:
    @classmethod
    def __call__(cls, extracted_data: ExtractedData, activity_data: dict[str, Any]) -> ExtractedData:
        result = extracted_data.model_copy()
        result.activity_id = activity_data.get('activity_id')
        result.activity_name = activity_data.get('activity_name')
        result.client_name = [val] if (val := activity_data.get('client_name')) else []
        result.ldmf_country = val if (val := activity_data.get('country')) else []
        # TODO: populate result with title
        result.start_date = cls._get_date_for_extracted_data(activity_data.get('engagement_start_date'))
        result.end_date = cls._get_date_for_extracted_data(activity_data.get('engagement_end_date'))
        result.start_date_original = activity_data.get('engagement_start_date_original')
        result.end_date_original = activity_data.get('engagement_end_date_original')
        result.client_industries = activity_data.get('client_industries')
        result.client_services = activity_data.get('client_services')
        result.team_roles = cls._get_json_for_extracted_data(activity_data, 'team_roles')
        result.objective_and_scope = None
        result.outcomes = None
        return result

    @staticmethod
    def _get_date_for_extracted_data(val: Any) -> date | None:
        if val is None or isinstance(val, date):
            result = val
        elif isinstance(val, str):
            try:
                result = date.fromisoformat(val)
            except ValueError:
                logger.warning('An activity date of an unexpected format was detected: %s', val)
                result = None
        else:
            logger.warning('An activity date of an unexpected type was detected: %s', type(val))
            result = None
        return result

    @staticmethod
    def _get_json_for_extracted_data(activity_data: dict[str, Any], key: str) -> str | None:
        result = activity_data.get(key)
        return json.dumps(result) if result else None
