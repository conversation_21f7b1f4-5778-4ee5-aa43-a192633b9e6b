from typing import Annotated

from fastapi import Depends

from constants.prompt import prompt_templates
from services import (
    AuthService,
    ClientIndustryDataService,
    ClientNameOptionHandlerService,
    ClientServiceDataService,
    CommandService,
    ConversationMessageService,
    ConversationService,
    DatesOptionHandlerService,
    DateValidatorService,
    DocumentService,
    EngagementFieldModificationService,
    ExtractedDataService,
    FeeAndCurrencyService,
    IntentClassifierService,
    KXDashService,
    KXDashTaskOptionHandlerService,
    LDMFCountryOptionHandlerService,
    LDMFCountryService,
    RoleDataService,
    SystemMessageGenerationService,
    TextEditService,
    TranslationService,
    UserInfoService,
)
from services.engagement_intent_processor import EngagementIntentProcessor

from .repositories import (
    BlobStorageRepositoryDep,
    ConversationMessageRepositoryDep,
    ConversationRepositoryDep,
    DocumentBlobStorageRepositoryDep,
    DocumentDbRepositoryDep,
    DocumentQueueRepositoryDep,
    ExtractedDataRepositoryDep,
    FieldRepositoryDep,
    IndustryRepositoryDep,
    KXDashRepositoryDep,
    LDMFCountriesCacheRepositoryDep,
    LDMFCountriesRepositoryDep,
    OpenAIRepositoryDep,
    ProcessingMessageRepositoryDep,
    QualsClientsRepositoryDep,
    RoleRepositoryDep,
    ServiceRepositoryDep,
    UserInfoRepositoryDep,
)


__all__ = [
    'AuthServiceDep',
    'ClientNameOptionHandlerServiceDep',
    'ClientIndustryDataServiceDep',
    'ConversationMessageServiceDep',
    'ConversationServiceDep',
    'DatesOptionHandlerServiceDep',
    'DocumentServiceDep',
    'EngagementFieldModificationServiceDep',
    'ExtractedDataServiceDep',
    'IntentClassifierServiceDep',
    'KXDashServiceDep',
    'KXDashTaskOptionHandlerServiceDep',
    'LDMFCountryOptionHandlerServiceDep',
    'LDMFCountryServiceDep',
    'SystemMessageGenerationServiceDep',
]


def get_auth_service() -> AuthService:
    return AuthService()


AuthServiceDep = Annotated[AuthService, Depends(get_auth_service)]


def get_intent_classifier_service(openai_service: OpenAIRepositoryDep) -> IntentClassifierService:
    """Get the intent classifier for dependency injection."""
    return IntentClassifierService(
        openai_service=openai_service,
        system_prompt_template=prompt_templates.prompt_page_intention.SYSTEM,
        user_prompt_template=prompt_templates.prompt_page_intention.USER,
        intentions=prompt_templates.prompt_page_intention.GENERAL_INTENTIONS,
        intent_object_structure=prompt_templates.prompt_page_intention.INTENT_OBJECT_STRUCTURE,
    )


IntentClassifierServiceDep = Annotated[IntentClassifierService, Depends(get_intent_classifier_service)]


def get_engagement_intent_classifier_service(openai_service: OpenAIRepositoryDep) -> IntentClassifierService:
    """Get the intent classifier for dependency injection."""
    return IntentClassifierService(
        openai_service=openai_service,
        system_prompt_template=prompt_templates.engagement_description_page_intention.SYSTEM,
        user_prompt_template=prompt_templates.engagement_description_page_intention.USER,
        intentions=prompt_templates.engagement_description_page_intention.INTENTIONS,
    )


EngagementIntentClassifierServiceDep = Annotated[
    IntentClassifierService, Depends(get_engagement_intent_classifier_service)
]


def get_translation_service(openai_service: OpenAIRepositoryDep) -> TranslationService:
    """Get the translation for dependency injection."""
    return TranslationService(openai_service=openai_service)


TranslationServiceDep = Annotated[TranslationService, Depends(get_translation_service)]


def get_client_industry_data_service(
    industry_repository: IndustryRepositoryDep,
    blob_repository: BlobStorageRepositoryDep,
) -> ClientIndustryDataService:
    return ClientIndustryDataService(industry_repository=industry_repository, blob_repository=blob_repository)


ClientIndustryDataServiceDep = Annotated[ClientIndustryDataService, Depends(get_client_industry_data_service)]


def get_client_service_data_service(
    service_repository: ServiceRepositoryDep, blob_repository: BlobStorageRepositoryDep
) -> ClientServiceDataService:
    return ClientServiceDataService(service_repository=service_repository, blob_repository=blob_repository)


ClientServiceDataServiceDep = Annotated[ClientServiceDataService, Depends(get_client_service_data_service)]


def get_role_data_service(
    role_repository: RoleRepositoryDep,
    blob_repository: BlobStorageRepositoryDep,
) -> RoleDataService:
    return RoleDataService(role_repository=role_repository, blob_repository=blob_repository)


RoleDataServiceDep = Annotated[RoleDataService, Depends(get_role_data_service)]


def get_service_data_service(
    service_repository: ServiceRepositoryDep,
) -> FeeAndCurrencyService:
    return FeeAndCurrencyService(service_repository=service_repository)


ServiceDataServiceDep = Annotated[FeeAndCurrencyService, Depends(get_service_data_service)]


def get_ldmf_country_service(
    ldmf_countries_repository: LDMFCountriesRepositoryDep,
    blob_repository: BlobStorageRepositoryDep,
    cache_repository: LDMFCountriesCacheRepositoryDep,
) -> LDMFCountryService:
    return LDMFCountryService(
        ldmf_countries_repository=ldmf_countries_repository,
        blob_repository=blob_repository,
        cache_repository=cache_repository,
    )


LDMFCountryServiceDep = Annotated[LDMFCountryService, Depends(get_ldmf_country_service)]


def get_user_info_service(
    user_info_repository: UserInfoRepositoryDep,
) -> UserInfoService:
    return UserInfoService(user_info_repository=user_info_repository)


UserInfoServiceDep = Annotated[UserInfoService, Depends(get_user_info_service)]


def get_extracted_data_service(
    extracted_data_repository: ExtractedDataRepositoryDep,
    industry_data_service: ClientIndustryDataServiceDep,
    role_data_service: RoleDataServiceDep,
    service_data_service: ServiceDataServiceDep,
    conversation_repository: ConversationRepositoryDep,
    quals_clients_repository: QualsClientsRepositoryDep,
    ldmf_country_service: LDMFCountryServiceDep,
    user_info_service: UserInfoServiceDep,
) -> ExtractedDataService:
    return ExtractedDataService(
        extracted_data_repository=extracted_data_repository,
        conversation_repository=conversation_repository,
        quals_clients_repository=quals_clients_repository,
        industry_data_service=industry_data_service,
        role_data_service=role_data_service,
        service_data_service=service_data_service,
        ldmf_country_service=ldmf_country_service,
        user_info_service=user_info_service,
    )


ExtractedDataServiceDep = Annotated[ExtractedDataService, Depends(get_extracted_data_service)]


def get_engagement_field_modification_service(
    extracted_data_service: ExtractedDataServiceDep,
    extracted_data_repository: ExtractedDataRepositoryDep,
    openai_repository: OpenAIRepositoryDep,
    field_repository: FieldRepositoryDep,
) -> EngagementFieldModificationService:
    return EngagementFieldModificationService(
        extracted_data_service=extracted_data_service,
        extracted_data_repository=extracted_data_repository,
        openai_repository=openai_repository,
        field_repository=field_repository,
    )


EngagementFieldModificationServiceDep = Annotated[
    EngagementFieldModificationService, Depends(get_engagement_field_modification_service)
]


def get_document_service(
    document_db_repository: DocumentDbRepositoryDep,
    blob_repository: DocumentBlobStorageRepositoryDep,
    document_queue_repository: DocumentQueueRepositoryDep,
    processing_message_repository: ProcessingMessageRepositoryDep,
) -> DocumentService:
    return DocumentService(
        document_db_repository=document_db_repository,
        blob_repository=blob_repository,
        document_queue_repository=document_queue_repository,
        processing_message_repository=processing_message_repository,
    )


DocumentServiceDep = Annotated[DocumentService, Depends(get_document_service)]


def get_date_validator_service(openai_service: OpenAIRepositoryDep) -> DateValidatorService:
    return DateValidatorService(openai_service=openai_service)


DateValidatorServiceDep = Annotated[DateValidatorService, Depends(get_date_validator_service)]


def get_kx_dash_service(
    kx_dash_repository: KXDashRepositoryDep,
    conversation_repository: ConversationRepositoryDep,
    extracted_data_service: ExtractedDataServiceDep,
    date_validator_service: DateValidatorServiceDep,
) -> KXDashService:
    return KXDashService(
        kx_dash_repository=kx_dash_repository,
        conversation_repository=conversation_repository,
        extracted_data_service=extracted_data_service,
        date_validator_service=date_validator_service,
    )


KXDashServiceDep = Annotated[KXDashService, Depends(get_kx_dash_service)]


def get_system_message_generation_service() -> SystemMessageGenerationService:
    """Get the system message generation service for dependency injection."""
    return SystemMessageGenerationService()


SystemMessageGenerationServiceDep = Annotated[
    SystemMessageGenerationService, Depends(get_system_message_generation_service)
]


def get_text_edit_service(openai_repository: OpenAIRepositoryDep) -> TextEditService:
    return TextEditService(openai_repository=openai_repository)


TextEditServiceDep = Annotated[TextEditService, Depends(get_text_edit_service)]


def get_engagement_intent_processor() -> EngagementIntentProcessor:
    return EngagementIntentProcessor()


EngagementIntentProcessorDep = Annotated[EngagementIntentProcessor, Depends(get_engagement_intent_processor)]


# Option Handler Services


def get_client_name_option_handler_service(
    extracted_data_service: ExtractedDataServiceDep,
    conversation_repository: ConversationRepositoryDep,
) -> ClientNameOptionHandlerService:
    return ClientNameOptionHandlerService(
        extracted_data_service=extracted_data_service,
        conversation_repository=conversation_repository,
    )


ClientNameOptionHandlerServiceDep = Annotated[
    ClientNameOptionHandlerService, Depends(get_client_name_option_handler_service)
]


def get_ldmf_country_option_handler_service(
    extracted_data_service: ExtractedDataServiceDep,
) -> LDMFCountryOptionHandlerService:
    return LDMFCountryOptionHandlerService(extracted_data_service=extracted_data_service)


LDMFCountryOptionHandlerServiceDep = Annotated[
    LDMFCountryOptionHandlerService, Depends(get_ldmf_country_option_handler_service)
]


def get_dates_option_handler_service(
    extracted_data_service: ExtractedDataServiceDep,
) -> DatesOptionHandlerService:
    return DatesOptionHandlerService(extracted_data_service=extracted_data_service)


DatesOptionHandlerServiceDep = Annotated[DatesOptionHandlerService, Depends(get_dates_option_handler_service)]


def get_kx_dash_task_option_handler_service(
    kx_dash_service: KXDashServiceDep,
) -> KXDashTaskOptionHandlerService:
    return KXDashTaskOptionHandlerService(kx_dash_service=kx_dash_service)


KXDashTaskOptionHandlerServiceDep = Annotated[
    KXDashTaskOptionHandlerService, Depends(get_kx_dash_task_option_handler_service)
]


def get_command_service(
    field_repository: FieldRepositoryDep,
    text_edit_service: TextEditServiceDep,
    openai_repository: OpenAIRepositoryDep,
    conversation_repository: ConversationRepositoryDep,
) -> CommandService:
    return CommandService(
        field_repository=field_repository,
        text_edit_service=text_edit_service,
        openai_repository=openai_repository,
        conversation_repository=conversation_repository,
    )


CommandServiceDep = Annotated[CommandService, Depends(get_command_service)]


def get_conversation_message_service(
    field_repository: FieldRepositoryDep,
    conversation_message_repository: ConversationMessageRepositoryDep,
    conversation_repository: ConversationRepositoryDep,
    document_service: DocumentServiceDep,
    document_db_repository: DocumentDbRepositoryDep,
    kx_dash_service: KXDashServiceDep,
    translation_service: TranslationServiceDep,
    extracted_data_service: ExtractedDataServiceDep,
    date_validator_service: DateValidatorServiceDep,
    system_message_generation_service: SystemMessageGenerationServiceDep,
    client_industry_service: ClientIndustryDataServiceDep,
    client_service_service: ClientServiceDataServiceDep,
    processing_message_repository: ProcessingMessageRepositoryDep,
    client_name_option_handler: ClientNameOptionHandlerServiceDep,
    ldmf_country_option_handler: LDMFCountryOptionHandlerServiceDep,
    dates_option_handler: DatesOptionHandlerServiceDep,
    kx_dash_task_option_handler: KXDashTaskOptionHandlerServiceDep,
    engagement_intent_processor: EngagementIntentProcessorDep,
    engagement_field_modification_service: EngagementFieldModificationServiceDep,
    intent_classifier_service: IntentClassifierServiceDep,
    openai_repository: OpenAIRepositoryDep,
    command_service: CommandServiceDep,
) -> ConversationMessageService:
    return ConversationMessageService(
        field_repository=field_repository,
        conversation_message_repository=conversation_message_repository,
        conversation_repository=conversation_repository,
        document_service=document_service,
        document_db_repository=document_db_repository,
        kx_dash_service=kx_dash_service,
        translation_service=translation_service,
        extracted_data_service=extracted_data_service,
        date_validator_service=date_validator_service,
        system_message_generation_service=system_message_generation_service,
        client_industry_service=client_industry_service,
        client_service_service=client_service_service,
        processing_message_repository=processing_message_repository,
        client_name_option_handler=client_name_option_handler,
        ldmf_country_option_handler=ldmf_country_option_handler,
        dates_option_handler=dates_option_handler,
        kx_dash_task_option_handler=kx_dash_task_option_handler,
        engagement_intent_processor=engagement_intent_processor,
        engagement_field_modification_service=engagement_field_modification_service,
        openai_repository=openai_repository,
        intent_classifier_service=intent_classifier_service,
        command_service=command_service,
    )


ConversationMessageServiceDep = Annotated[ConversationMessageService, Depends(get_conversation_message_service)]


def get_conversation_service(
    conversation_repository: ConversationRepositoryDep,
    conversation_message_service: ConversationMessageServiceDep,
    extracted_data_service: ExtractedDataServiceDep,
    document_service: DocumentServiceDep,
    kx_dash_service: KXDashServiceDep,
) -> ConversationService:
    return ConversationService(
        conversation_repository=conversation_repository,
        conversation_message_service=conversation_message_service,
        extracted_data_service=extracted_data_service,
        document_service=document_service,
        kx_dash_service=kx_dash_service,
    )


ConversationServiceDep = Annotated[ConversationService, Depends(get_conversation_service)]
