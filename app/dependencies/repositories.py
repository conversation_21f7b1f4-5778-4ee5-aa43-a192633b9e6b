from typing import Annotated, Optional, Type

from fastapi import Depends

from config import settings
from repositories import (
    BlobStorageRepository,
    ConversationMessageRepository,
    ConversationRepository,
    DocumentBlobStorageRepository,
    DocumentDbRepository,
    DocumentQueueRepository,
    ExtractedDataRepository,
    FieldRepository,
    IndustryRepository,
    InMemoryCacheRepository,
    KXDashRepository,
    LDMFCountriesRepository,
    OpenAIRepository,
    ProcessingMessageRepository,
    QualsClientsRepository,
    RoleRepository,
    ServiceRepository,
    UserInfoRepository,
)

from .db import DbSessionDep
from .http_client import HTTPClientDep


__all__ = [
    'CacheRepositoryDep',
    'QualsClientsCacheRepositoryDep',
    'ConversationRepositoryDep',
    'ConversationMessageRepositoryDep',
    'DocumentDbRepositoryDep',
    'BlobStorageRepositoryDep',
    'DocumentBlobStorageRepositoryDep',
    'QualsClientsRepositoryDep',
    'KXDashRepositoryDep',
    'ExtractedDataRepositoryDep',
    'DocumentQueueRepositoryDep',
    'IndustryRepositoryDep',
    'RoleRepositoryDep',
    'UserInfoRepositoryDep',
    'ServiceRepositoryDep',
    'LDMFCountriesRepositoryDep',
    'LDMFCountriesCacheRepositoryDep',
]


# Cache repository singletons to ensure shared cache instances
_default_cache_repository: Optional[InMemoryCacheRepository] = None
_quals_clients_cache_repository: Optional[InMemoryCacheRepository] = None


def get_default_cache_repository() -> InMemoryCacheRepository:
    """Get the default cache repository for dependency injection."""
    global _default_cache_repository
    if _default_cache_repository is None:
        _default_cache_repository = InMemoryCacheRepository(
            maxsize=settings.cache.default_maxsize,
            ttl=settings.cache.default_ttl,
            key_prefix='default:',
        )
    return _default_cache_repository


CacheRepositoryDep = Annotated[InMemoryCacheRepository, Depends(get_default_cache_repository)]


LDMFCountriesCacheRepositoryDep = Annotated[InMemoryCacheRepository, Depends(get_default_cache_repository)]


def get_quals_clients_cache_repository() -> InMemoryCacheRepository:
    """Get the Quals clients cache repository for dependency injection."""
    global _quals_clients_cache_repository
    if _quals_clients_cache_repository is None:
        _quals_clients_cache_repository = InMemoryCacheRepository(
            maxsize=settings.cache.quals_clients_maxsize,
            ttl=settings.cache.quals_clients_ttl,
            key_prefix='quals_clients:',
        )
    return _quals_clients_cache_repository


QualsClientsCacheRepositoryDep = Annotated[InMemoryCacheRepository, Depends(get_quals_clients_cache_repository)]


def get_openai_repository() -> OpenAIRepository:
    """Get the OpenAI service for dependency injection."""
    return OpenAIRepository()


OpenAIRepositoryDep = Annotated[OpenAIRepository, Depends(get_openai_repository)]


def get_conversation_repository(db_session: DbSessionDep) -> ConversationRepository:
    return ConversationRepository(db_session)


ConversationRepositoryDep = Annotated[ConversationRepository, Depends(get_conversation_repository)]


def get_processing_message_repository(db_session: DbSessionDep) -> ProcessingMessageRepository:
    return ProcessingMessageRepository(db_session=db_session)


ProcessingMessageRepositoryDep = Annotated[ProcessingMessageRepository, Depends(get_processing_message_repository)]


def get_conversation_message_repository(
    db_session: DbSessionDep, conversation_repository: ConversationRepositoryDep
) -> ConversationMessageRepository:
    return ConversationMessageRepository(db_session=db_session, conversation_repository=conversation_repository)


ConversationMessageRepositoryDep = Annotated[
    ConversationMessageRepository, Depends(get_conversation_message_repository)
]


def get_document_db_repository(db_session: DbSessionDep) -> DocumentDbRepository:
    return DocumentDbRepository(db_session)


DocumentDbRepositoryDep = Annotated[DocumentDbRepository, Depends(get_document_db_repository)]


class BlobRepositoryGetter:
    def __init__(
        self,
        repo_class: Type[BlobStorageRepository],
        container_name: str = settings.blob_storage.default_container_name,
    ):
        self._repo_class = repo_class
        self._container_name = container_name

    async def __call__(self) -> BlobStorageRepository:
        repo = self._repo_class(settings.blob_storage.connection_string, self._container_name)
        await repo.initialize()
        return repo


BlobStorageRepositoryDep = Annotated[BlobStorageRepository, Depends(BlobRepositoryGetter(BlobStorageRepository))]
DocumentBlobStorageRepositoryDep = Annotated[
    DocumentBlobStorageRepository,
    Depends(
        BlobRepositoryGetter(
            DocumentBlobStorageRepository, container_name=settings.blob_storage.document_container_name
        )
    ),
]


def get_kx_dash_repository(http_client: HTTPClientDep) -> KXDashRepository:
    return KXDashRepository(http_client)


KXDashRepositoryDep = Annotated[KXDashRepository, Depends(get_kx_dash_repository)]


def get_quals_clients_repository(
    http_client: HTTPClientDep, cache_repository: QualsClientsCacheRepositoryDep
) -> QualsClientsRepository:
    return QualsClientsRepository(http_client, cache_repository)


QualsClientsRepositoryDep = Annotated[QualsClientsRepository, Depends(get_quals_clients_repository)]


def get_ldmf_countries_repository(http_client: HTTPClientDep) -> LDMFCountriesRepository:
    return LDMFCountriesRepository(http_client, str(settings.ldmf_countries_api.base_url))


LDMFCountriesRepositoryDep = Annotated[LDMFCountriesRepository, Depends(get_ldmf_countries_repository)]


def get_extracted_data_repository(
    db_session: DbSessionDep, conversation_repository: ConversationRepositoryDep
) -> ExtractedDataRepository:
    return ExtractedDataRepository(db_session=db_session, conversation_repository=conversation_repository)


ExtractedDataRepositoryDep = Annotated[ExtractedDataRepository, Depends(get_extracted_data_repository)]


async def get_document_queue_repository() -> DocumentQueueRepository:
    return DocumentQueueRepository(
        settings.document_queue.connection_string,
        settings.document_queue.content_queue_name,
    )


DocumentQueueRepositoryDep = Annotated[DocumentQueueRepository, Depends(get_document_queue_repository)]


def get_industry_repository(http_client: HTTPClientDep) -> IndustryRepository:
    return IndustryRepository(http_client)


IndustryRepositoryDep = Annotated[IndustryRepository, Depends(get_industry_repository)]


def get_role_repository(http_client: HTTPClientDep) -> RoleRepository:
    return RoleRepository(http_client)


RoleRepositoryDep = Annotated[RoleRepository, Depends(get_role_repository)]


def get_service_repository(http_client: HTTPClientDep) -> ServiceRepository:
    return ServiceRepository(http_client)


ServiceRepositoryDep = Annotated[ServiceRepository, Depends(get_service_repository)]


def get_user_info_repository(http_client: HTTPClientDep) -> UserInfoRepository:
    return UserInfoRepository(http_client)


UserInfoRepositoryDep = Annotated[UserInfoRepository, Depends(get_user_info_repository)]


def get_field_repository(
    db_session: DbSessionDep, conversation_repository: ConversationRepositoryDep
) -> FieldRepository:
    return FieldRepository(db_session, conversation_repository)


FieldRepositoryDep = Annotated[FieldRepository, Depends(get_field_repository)]
