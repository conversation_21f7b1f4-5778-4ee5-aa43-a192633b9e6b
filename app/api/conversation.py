import logging
from typing import Sequence
from uuid import UUID

from fastapi import APIRouter, Request, status

from config import settings
from constants.environment import Environment
from constants.message import PageType
from constants.operation_ids import operation_ids
from dependencies import (
    ConversationMessageServiceDep,
    ConversationServiceDep,
    OwnerOnlyPermissionDep,
)
from schemas import (
    BaseMessageSerializer,
    CombinedExtractedDataResponse,
    ConversationCreationRequest,
    ConversationExtraData,
    ConversationQualIdUpdateRequest,
    ConversationResponse,
    ConversationWithWelcomeMessageResponse,
    MessageSerializer,
)


__all__ = ['router']


logger = logging.getLogger(__name__)

router = APIRouter(prefix='/conversations')


@router.post(
    '',
    operation_id=operation_ids.conversation.CREATE,
    status_code=status.HTTP_201_CREATED,
)
async def create_conversation_with_welcome_message(
    conversation_data: ConversationCreationRequest,
    conversation_service: ConversationServiceDep,
    request: Request,
) -> ConversationWithWelcomeMessageResponse:
    """
    Create a new conversation with a welcome system message.

    This endpoint creates a new conversation and automatically adds a system welcome message.
    """
    authorization_header_value: str = request.headers.get('Authorization', '')
    _, _, auth_token = authorization_header_value.partition(' ')

    return await conversation_service.create_with_welcome_message(
        conversation_data, user_id=request.state.user.id, user_name=request.state.user.full_name, token=auth_token
    )


@router.get(
    '/{conversation_id}',
    operation_id=operation_ids.conversation.GET,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def get_conversation_by_id(
    conversation_id: UUID,
    conversation_service: ConversationServiceDep,
) -> ConversationResponse:
    """
    Get a conversation by its ID.
    """
    return await conversation_service.get(conversation_id)


if settings.environment in (Environment.LOCAL, Environment.DEV, Environment.QA, Environment.TEST):

    @router.get(
        '/{conversation_id}/extra',
        operation_id=operation_ids.conversation.GET_EXTRA_DATA,
    )
    async def get_conversation_extra_data(
        conversation_id: UUID,
        conversation_service: ConversationServiceDep,
    ) -> ConversationExtraData:  # pragma: no cover
        """
        Get a conversation by its ID.
        """
        return await conversation_service.get_extra_data(conversation_id)


@router.get(
    '/{conversation_id}/messages',
    operation_id=operation_ids.message.LIST,
    status_code=status.HTTP_200_OK,
    response_model=Sequence[MessageSerializer],
    dependencies=(OwnerOnlyPermissionDep,),
)
async def list_messages(
    conversation_id: UUID,
    message_service: ConversationMessageServiceDep,
    page_type: PageType | None = None,
) -> Sequence[BaseMessageSerializer]:
    """
    Get all messages for a specific conversation.

    Optionally filters messages by page_type.
    """
    return await message_service.list(conversation_id, page_type=page_type)


@router.delete(
    '/{conversation_id}',
    operation_id=operation_ids.conversation.DELETE,
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def delete_conversation(
    conversation_id: UUID,
    conversation_service: ConversationServiceDep,
) -> None:
    """
    Delete a conversation by its ID.

    This operation also deletes all messages associated with the conversation.
    """
    await conversation_service.delete(conversation_id)


@router.get(
    '/{conversation_id}/messages/last',
    operation_id=operation_ids.message.GET_LAST,
    status_code=status.HTTP_200_OK,
    response_model=MessageSerializer,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def get_last_message(
    request: Request,
    conversation_id: UUID,
    message_service: ConversationMessageServiceDep,
) -> BaseMessageSerializer:
    """
    Get the last message for a specific conversation.

    If the last message is from a user, automatically generates and creates
    a system message with extracted data information.
    """
    authorization_header_value: str = request.headers.get('Authorization', '')
    _, _, auth_token = authorization_header_value.partition(' ')
    return await message_service.get_last(conversation_id, auth_token)


@router.get(
    '/{conversation_id}/extracted-data-summary',
    operation_id=operation_ids.extracted_data_summary.GET,
    response_model=CombinedExtractedDataResponse,
    status_code=status.HTTP_200_OK,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def get_combined_extracted_data(
    conversation_id: UUID,
    request: Request,
    conversation_service: ConversationServiceDep,
) -> CombinedExtractedDataResponse:
    """
    Get combined extracted and aggregated data for a specific conversation.
    """
    # Extract token from Authorization header
    token = request.headers.get('Authorization', '').replace('Bearer ', '')

    return await conversation_service.get_combined_extracted_data(conversation_id, token)


@router.get(
    '/qual/{qual_id}/extracted-data-summary',
    operation_id=operation_ids.extracted_data_summary.GET_BY_QUAL_ID,
    response_model=CombinedExtractedDataResponse,
    status_code=status.HTTP_200_OK,
)
async def get_combined_extracted_data_by_qual_id(
    qual_id: str,
    request: Request,
    conversation_service: ConversationServiceDep,
) -> CombinedExtractedDataResponse:
    """
    Get combined extracted and aggregated data for a specific conversation by QualId.
    """
    # Extract token from Authorization header
    token = request.headers.get('Authorization', '').replace('Bearer ', '')

    return await conversation_service.get_combined_extracted_data_by_qual_id(qual_id, token)


@router.patch(
    '/{conversation_id}',
    operation_id=operation_ids.conversation.UPDATE_QUAL_ID,
    status_code=status.HTTP_200_OK,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def update_conversation_qual_id(
    conversation_id: UUID,
    qual_id_update: ConversationQualIdUpdateRequest,
    conversation_service: ConversationServiceDep,
) -> ConversationResponse:
    """
    Update the QualId for a conversation.
    """
    return await conversation_service.update_qual_id(conversation_id, qual_id_update.qual_id)


@router.post(
    '/{conversation_id}/chats/engagement-descriptions',
    operation_id=operation_ids.conversation.ENGAGEMENT_CHAT_CREATE,
    status_code=status.HTTP_200_OK,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def initialize_engagement_description_chat(
    conversation_id: UUID,
    conversation_service: ConversationServiceDep,
) -> None:
    """
    Initialize the chat for the engagement description, details, teams and usage pages.
    """
    return await conversation_service.initialize_engagement_chats(conversation_id)
