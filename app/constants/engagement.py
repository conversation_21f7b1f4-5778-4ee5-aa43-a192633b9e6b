from dataclasses import dataclass

from core.enum import StrEnum
from core.utils import load_file_from_folder


__all__ = [
    'engagement_templates',
    'EngagementMessageIntention',
    'EngagementFeeDisplay',
    'StructuredSuggestedPromptTitle',
    'StructuredSuggestedPromptBody',
]

FIRST_WELCOME_MESSAGE = load_file_from_folder('engagement_details', 'welcome_message_assistant.txt')
EDITED_TEXT_FIELD_REPLY = load_file_from_folder('engagement_details', 'edited_text_field.txt')
OTHER_PAGE_REPLY = load_file_from_folder('engagement_details', 'other_page.txt')
UNDEFINED_REPLY = load_file_from_folder('engagement_details', 'undefined_reply.txt')
KX_PROMPT_REPLY = load_file_from_folder('engagement_details', 'kx_prompt_reply.txt')

SORRY_CANT_HELP_REPLY = "Sorry, I can't help with this. Please make the necessary edits on the right side of the page."
UNDO_SUCCESS = 'Undo successful. The previous text has been restored.'
CLIENT_NAME_REPLACED = (
    "In order to maintain confidentiality and facilitate external use, I have replaced the client's"
    ' name (“{client_name}”) with the term “the client” in all engagement descriptions.'
)


@dataclass(frozen=True)
class WelcomeMessageTemplates:
    SYSTEM: str = FIRST_WELCOME_MESSAGE


@dataclass(frozen=True)
class EngagementDescriptionTemplates:
    """Class to hold engagement templates."""

    edited_text_field_reply: str = EDITED_TEXT_FIELD_REPLY
    other_page_reply: str = OTHER_PAGE_REPLY
    undefined_reply: str = UNDEFINED_REPLY
    kx_prompt_reply: str = KX_PROMPT_REPLY
    sorry_cant_help_reply: str = SORRY_CANT_HELP_REPLY
    undo_success: str = UNDO_SUCCESS
    client_name_replaced: str = CLIENT_NAME_REPLACED


@dataclass(frozen=True)
class EngagementTemplates:
    welcome: WelcomeMessageTemplates = WelcomeMessageTemplates()
    general: EngagementDescriptionTemplates = EngagementDescriptionTemplates()


engagement_templates = EngagementTemplates()


class EngagementMessageIntention(StrEnum):
    ENGAGEMENT_TITLE = 'engagement_title'
    BUSINESS_ISSUES = 'business_issues'
    SCOPE_APPROACH = 'scope_approach'
    VALUE_DELIVERED_IMPACT = 'value_delivered_impact'
    ENGAGEMENT_SUMMARY = 'engagement_summary'
    ONE_LINE_DESCRIPTION = 'one_line_description'
    NAVIGATE_TO_RESOURCE_PAGE = 'navigate_to_resource_page'
    UNDEFINED = 'undefined'
    UNKNOWN_FIELD = 'unknown_field'


class EngagementFeeDisplay(StrEnum):
    ACTUAL_AMOUNT = 'Actual Amount'
    CONFIDENTIAL = 'Confidential (do not display)'
    FEE_RANGE = 'Fee Range'


class StructuredSuggestedPromptTitle(StrEnum):
    """Schema for structured suggested prompt title."""

    EXPAND_SCOPE_AND_APPROACH = 'Consider expanding on the scope and approach'
    EXPAND_BUSINESS_ISSUES = 'Consider expanding on the business issues'
    ADD_QUANTITATIVE_INFO = 'Consider adding quantitative or qualitative information'


class StructuredSuggestedPromptBody(StrEnum):
    """Schema for structured suggested prompt body."""

    EXPAND_SCOPE_AND_APPROACH = (
        "This provides a clearer understanding of the approach Deloitte used to resolve the client's business issues."
    )
    EXPAND_BUSINESS_ISSUES = (
        'This provides a clearer understanding of the business challenges that led to this engagement opportunity.'
    )
    ADD_QUANTITATIVE_INFO = (
        'Incorporating quantitative or qualitative data reinforces the value delivered, such as ROI or cost reduction.'
    )
