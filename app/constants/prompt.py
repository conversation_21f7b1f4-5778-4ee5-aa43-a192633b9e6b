from dataclasses import dataclass, field
import json

from core.utils import load_file_from_folder


__all__ = [
    'EXTRACT_DATA_SYSTEM_PROMPT',
    'EXTRACT_DATA_USER_PROMPT',
    'EXTRACT_DATES_SYSTEM_PROMPT',
    'EXTRACT_DATES_USER_PROMPT',
    'EXTRACT_DATES_DATE_IS_TEXT_SYSTEM_PROMPT',
    'EXTRACT_DATES_DATE_IS_TEXT_USER_PROMPT',
    'EXTRACT_LDMF_COUNTRY_SYSTEM_PROMPT',
    'EXTRACT_LDMF_COUNTRY_USER_PROMPT',
    'prompt_templates',
]

EXTRACT_DATA_SYSTEM_PROMPT = load_file_from_folder('extract_data_prompts', 'extract_data_system_prompt.txt')
EXTRACT_DATA_USER_PROMPT = load_file_from_folder('extract_data_prompts', 'extract_data_user_prompt.txt')
EXTRACT_DATES_SYSTEM_PROMPT = load_file_from_folder('extract_data_prompts', 'extract_dates_system_prompt.txt')
EXTRACT_DATES_USER_PROMPT = load_file_from_folder('extract_data_prompts', 'extract_dates_user_prompt.txt')
EXTRACT_DATES_DATE_IS_TEXT_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_dates_date_is_text_system_prompt.txt'
)
EXTRACT_DATES_DATE_IS_TEXT_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_dates_date_is_text_user_prompt.txt'
)
EXTRACT_LDMF_COUNTRY_SYSTEM_PROMPT = load_file_from_folder(
    'ldmf_country_prompts', 'extract_and_validate_countries_data_system_prompt.txt'
)
EXTRACT_LDMF_COUNTRY_USER_PROMPT = load_file_from_folder(
    'ldmf_country_prompts', 'extract_and_validate_countries_user_prompt.txt'
)
EXTRACT_CLIENT_REFERENCES_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_client_references_system_prompt.txt'
)
EXTRACT_CLIENT_REFERENCES_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_client_references_user_prompt.txt'
)
CLIENT_NAME_SHARING_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'client_name_sharing_system_prompt.txt'
)
CLIENT_NAME_SHARING_USER_PROMPT = load_file_from_folder('extract_data_prompts', 'client_name_sharing_user_prompt.txt')
EXTRACT_QUAL_USAGES_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_qual_usages_system_prompt.txt'
)
EXTRACT_QUAL_USAGES_USER_PROMPT = load_file_from_folder('extract_data_prompts', 'extract_qual_usages_user_prompt.txt')
EXTRACT_PROJECT_ROLES_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_team_and_roles_system_prompt.txt'
)
EXTRACT_PROJECT_ROLES_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_team_and_roles_user_prompt.txt'
)

VALUE_AND_IMPACT_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts/engagement_details', 'value_and_impact_system_prompt.txt'
)
VALUE_AND_IMPACT_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts/engagement_details', 'value_and_impact_user_prompt.txt'
)
EXTRACT_CLIENT_INDUSTRY_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_client_industry_user_prompt.txt'
)
EXTRACT_CLIENT_INDUSTRY_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_client_industry_system_prompt.txt'
)
EXTRACT_CLIENT_SERVICES_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_client_services_user_prompt.txt'
)
EXTRACT_CLIENT_SERVICES_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_client_services_system_prompt.txt'
)
EXTRACT_ENGAGEMENT_LOCATIONS_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_engagement_locations_user_prompt.txt'
)
EXTRACT_ENGAGEMENT_LOCATIONS_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_engagement_locations_system_prompt.txt'
)
EXTRACT_SOURCE_OF_WORK_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_source_of_work_user_prompt.txt'
)
EXTRACT_SOURCE_OF_WORK_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_source_of_work_system_prompt.txt'
)

PROMPT_PAGE_INTENTION_SYSTEM_PROMPT = load_file_from_folder('prompt_page_intention_prompts', 'system_prompt.txt')
PROMPT_PAGE_INTENTION_USER_PROMPT = load_file_from_folder('prompt_page_intention_prompts', 'user_prompt.txt')
INTENT_OBJECT_STRUCTURE = load_file_from_folder('intentions', 'intent_object_structure.txt')
GENERAL_INTENTIONS = json.loads(load_file_from_folder('intentions', 'general_intentions.json'))

ENGAGEMENT_DESCRIPTION_INTENTION_SYSTEM_PROMPT = load_file_from_folder(
    'engagement_description_page_intention_prompts', 'system_prompt.txt'
)
ENGAGEMENT_DESCRIPTION_INTENTION_USER_PROMPT = load_file_from_folder(
    'engagement_description_page_intention_prompts', 'user_prompt.txt'
)
ENGAGEMENT_INTENTIONS = json.loads(load_file_from_folder('intentions', 'engagement_intentions.json'))

ENGAGEMENT_FIELD_CHANGER_SYSTEM_PROMPT = load_file_from_folder('engagement_fields_changer_prompts', 'system_prompt.txt')
ENGAGEMENT_FIELD_CHANGER_USER_PROMPT = load_file_from_folder('engagement_fields_changer_prompts', 'user_prompt.txt')

ENGAGEMENT_FEE_SYSTEM_PROMPT = load_file_from_folder('engagement_fee_prompts', 'system_prompt.txt')
ENGAGEMENT_FEE_USER_PROMPT = load_file_from_folder('engagement_fee_prompts', 'user_prompt.txt')
EXTRACT_BUSINESS_ISSUES_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts/engagement_details', 'business_issues_system_prompt.txt'
)
EXTRACT_BUSINESS_ISSUES_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts/engagement_details', 'business_issues_user_prompt.txt'
)
EXTRACT_SCOPE_AND_APPROACH_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts/engagement_details', 'scope_and_approach_system_prompt.txt'
)
EXTRACT_SCOPE_AND_APPROACH_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts/engagement_details', 'scope_and_approach_user_prompt.txt'
)
EXTRACT_ENGAGEMENT_SUMMARY_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts/engagement_details', 'engagement_summary_system_prompt.txt'
)
EXTRACT_ENGAGEMENT_SUMMARY_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts/engagement_details', 'engagement_summary_user_prompt.txt'
)
EXTRACT_ONE_LINE_SUMMARY_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts/engagement_details', 'one_line_summary_system_prompt.txt'
)
EXTRACT_ONE_LINE_SUMMARY_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts/engagement_details', 'one_line_summary_user_prompt.txt'
)

TEXT_EDIT_EXPAND_SYSTEM_PROMPT = load_file_from_folder('text_editor_prompts/expand', 'expand_system_prompt.txt')
TEXT_EDIT_EXPAND_USER_PROMPT = load_file_from_folder('text_editor_prompts/expand', 'expand_user_prompt.txt')
TEXT_EDIT_REWRITE_SYSTEM_PROMPT = load_file_from_folder('text_editor_prompts/rewrite', 'rewrite_system_prompt.txt')
TEXT_EDIT_REWRITE_USER_PROMPT = load_file_from_folder('text_editor_prompts/rewrite', 'rewrite_user_prompt.txt')
TEXT_EDIT_SHORTEN_SYSTEM_PROMPT = load_file_from_folder('text_editor_prompts/shorten', 'shorten_system_prompt.txt')
TEXT_EDIT_SHORTEN_USER_PROMPT = load_file_from_folder('text_editor_prompts/shorten', 'shorten_user_prompt.txt')
TEXT_EDIT_PROMPT_SYSTEM_PROMPT = load_file_from_folder(
    'text_editor_prompts/ask_contribution_assistant', 'ask_contribution_assistant_system.txt'
)
TEXT_EDIT_PROMPT_USER_PROMPT = load_file_from_folder(
    'text_editor_prompts/ask_contribution_assistant', 'ask_contribution_assistant_user.txt'
)


@dataclass(frozen=True)
class ExtractDataPrompts:
    SYSTEM: str = EXTRACT_DATA_SYSTEM_PROMPT
    USER: str = EXTRACT_DATA_USER_PROMPT


@dataclass(frozen=True)
class ExtractDatesPrompts:
    SYSTEM: str = EXTRACT_DATES_SYSTEM_PROMPT
    USER: str = EXTRACT_DATES_USER_PROMPT
    DATE_IS_TEXT_SYSTEM: str = EXTRACT_DATES_DATE_IS_TEXT_SYSTEM_PROMPT
    DATE_IS_TEXT_USER: str = EXTRACT_DATES_DATE_IS_TEXT_USER_PROMPT


@dataclass(frozen=True)
class ExtractLDMFCountryPrompts:
    SYSTEM: str = EXTRACT_LDMF_COUNTRY_SYSTEM_PROMPT
    USER: str = EXTRACT_LDMF_COUNTRY_USER_PROMPT


@dataclass(frozen=True)
class ExtractClientReferencesPrompts:
    SYSTEM: str = EXTRACT_CLIENT_REFERENCES_SYSTEM_PROMPT
    USER: str = EXTRACT_CLIENT_REFERENCES_USER_PROMPT


@dataclass(frozen=True)
class ExtractClientNameSharingPrompts:
    SYSTEM: str = CLIENT_NAME_SHARING_SYSTEM_PROMPT
    USER: str = CLIENT_NAME_SHARING_USER_PROMPT


@dataclass(frozen=True)
class ExtractQualUsagePrompts:
    SYSTEM: str = EXTRACT_QUAL_USAGES_SYSTEM_PROMPT
    USER: str = EXTRACT_QUAL_USAGES_USER_PROMPT


@dataclass(frozen=True)
class ExtractClientIndustryPrompts:
    SYSTEM: str = EXTRACT_CLIENT_INDUSTRY_SYSTEM_PROMPT
    USER: str = EXTRACT_CLIENT_INDUSTRY_USER_PROMPT


@dataclass(frozen=True)
class ExtractClientServicesPrompts:
    SYSTEM: str = EXTRACT_CLIENT_SERVICES_SYSTEM_PROMPT
    USER: str = EXTRACT_CLIENT_SERVICES_USER_PROMPT


@dataclass(frozen=True)
class ExtractEngagementLocationsPrompts:
    SYSTEM: str = EXTRACT_ENGAGEMENT_LOCATIONS_SYSTEM_PROMPT
    USER: str = EXTRACT_ENGAGEMENT_LOCATIONS_USER_PROMPT


@dataclass(frozen=True)
class ExtractSourceOfWorkPrompts:
    SYSTEM: str = EXTRACT_SOURCE_OF_WORK_SYSTEM_PROMPT
    USER: str = EXTRACT_SOURCE_OF_WORK_USER_PROMPT


@dataclass(frozen=True)
class PromptPageIntentionPrompts:
    SYSTEM: str = PROMPT_PAGE_INTENTION_SYSTEM_PROMPT
    USER: str = PROMPT_PAGE_INTENTION_USER_PROMPT
    INTENT_OBJECT_STRUCTURE: str = INTENT_OBJECT_STRUCTURE
    GENERAL_INTENTIONS: dict = field(default_factory=lambda: GENERAL_INTENTIONS)


@dataclass(frozen=True)
class ExtractProjectRolesPrompts:
    SYSTEM: str = EXTRACT_PROJECT_ROLES_SYSTEM_PROMPT
    USER: str = EXTRACT_PROJECT_ROLES_USER_PROMPT


@dataclass(frozen=True)
class ExtractBusinessIssuesPrompts:
    SYSTEM: str = EXTRACT_BUSINESS_ISSUES_SYSTEM_PROMPT
    USER: str = EXTRACT_BUSINESS_ISSUES_USER_PROMPT


@dataclass(frozen=True)
class ValueAndImpactPrompts:
    SYSTEM: str = VALUE_AND_IMPACT_SYSTEM_PROMPT
    USER: str = VALUE_AND_IMPACT_USER_PROMPT


@dataclass(frozen=True)
class EngagementDescriptionPageIntentionPrompts:
    SYSTEM: str = ENGAGEMENT_DESCRIPTION_INTENTION_SYSTEM_PROMPT
    USER: str = ENGAGEMENT_DESCRIPTION_INTENTION_USER_PROMPT
    INTENTIONS: dict = field(default_factory=lambda: ENGAGEMENT_INTENTIONS)
    INTENT_OBJECT_STRUCTURE: str = INTENT_OBJECT_STRUCTURE


@dataclass(frozen=True)
class EngagementFieldChangerPrompts:
    SYSTEM: str = ENGAGEMENT_FIELD_CHANGER_SYSTEM_PROMPT
    USER: str = ENGAGEMENT_FIELD_CHANGER_USER_PROMPT


@dataclass(frozen=True)
class EngagementFeePrompts:
    SYSTEM: str = ENGAGEMENT_FEE_SYSTEM_PROMPT
    USER: str = ENGAGEMENT_FEE_USER_PROMPT


@dataclass(frozen=True)
class ExtractScopeAndApproachPrompts:
    SYSTEM: str = EXTRACT_SCOPE_AND_APPROACH_SYSTEM_PROMPT
    USER: str = EXTRACT_SCOPE_AND_APPROACH_USER_PROMPT


@dataclass(frozen=True)
class ExtractEngagementSummaryPrompts:
    SYSTEM: str = EXTRACT_ENGAGEMENT_SUMMARY_SYSTEM_PROMPT
    USER: str = EXTRACT_ENGAGEMENT_SUMMARY_USER_PROMPT


@dataclass(frozen=True)
class TextEditExpandPrompts:
    SYSTEM: str = TEXT_EDIT_EXPAND_SYSTEM_PROMPT
    USER: str = TEXT_EDIT_EXPAND_USER_PROMPT


@dataclass(frozen=True)
class TextEditRewritePrompts:
    SYSTEM: str = TEXT_EDIT_REWRITE_SYSTEM_PROMPT
    USER: str = TEXT_EDIT_REWRITE_USER_PROMPT


@dataclass(frozen=True)
class TextEditShortenPrompts:
    SYSTEM: str = TEXT_EDIT_SHORTEN_SYSTEM_PROMPT
    USER: str = TEXT_EDIT_SHORTEN_USER_PROMPT


@dataclass(frozen=True)
class TextEditPromptPrompts:
    SYSTEM: str = TEXT_EDIT_PROMPT_SYSTEM_PROMPT
    USER: str = TEXT_EDIT_PROMPT_USER_PROMPT


@dataclass(frozen=True)
class ExtractOneLineSummaryPrompts:
    SYSTEM: str = EXTRACT_ONE_LINE_SUMMARY_SYSTEM_PROMPT
    USER: str = EXTRACT_ONE_LINE_SUMMARY_USER_PROMPT


@dataclass(frozen=True)
class PromptTemplates:
    extract_data: ExtractDataPrompts = ExtractDataPrompts()
    extract_dates: ExtractDatesPrompts = ExtractDatesPrompts()
    extract_ldmf_country: ExtractLDMFCountryPrompts = ExtractLDMFCountryPrompts()
    extract_client_references: ExtractClientReferencesPrompts = ExtractClientReferencesPrompts()
    extract_client_name_sharing: ExtractClientNameSharingPrompts = ExtractClientNameSharingPrompts()
    extract_qual_usage: ExtractQualUsagePrompts = ExtractQualUsagePrompts()
    prompt_page_intention: PromptPageIntentionPrompts = PromptPageIntentionPrompts()
    extract_project_roles: ExtractProjectRolesPrompts = ExtractProjectRolesPrompts()
    engagement_description_page_intention: EngagementDescriptionPageIntentionPrompts = (
        EngagementDescriptionPageIntentionPrompts()
    )
    engagement_field_changer: EngagementFieldChangerPrompts = EngagementFieldChangerPrompts()
    engagement_fee: EngagementFeePrompts = EngagementFeePrompts()
    extract_business_issues: ExtractBusinessIssuesPrompts = ExtractBusinessIssuesPrompts()
    extract_scope_and_approach: ExtractScopeAndApproachPrompts = ExtractScopeAndApproachPrompts()
    extract_value_and_impact: ValueAndImpactPrompts = ValueAndImpactPrompts()
    extract_source_of_work: ExtractSourceOfWorkPrompts = ExtractSourceOfWorkPrompts()
    extract_engagement_summary: ExtractEngagementSummaryPrompts = ExtractEngagementSummaryPrompts()
    extract_one_line_summary: ExtractOneLineSummaryPrompts = ExtractOneLineSummaryPrompts()
    extract_client_industries: ExtractClientIndustryPrompts = ExtractClientIndustryPrompts()
    extract_client_services: ExtractClientServicesPrompts = ExtractClientServicesPrompts()
    extract_engagement_locations: ExtractEngagementLocationsPrompts = ExtractEngagementLocationsPrompts()
    text_edit_expand: TextEditExpandPrompts = TextEditExpandPrompts()
    text_edit_rewrite: TextEditRewritePrompts = TextEditRewritePrompts()
    text_edit_shorten: TextEditShortenPrompts = TextEditShortenPrompts()
    text_edit_prompt: TextEditPromptPrompts = TextEditPromptPrompts()


prompt_templates = PromptTemplates()
