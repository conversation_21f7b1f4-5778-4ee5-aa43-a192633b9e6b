import logging
from typing import Any

from config import settings
from core.http_client import CustomAsyncClient
from core.urls import url_join
from schemas.service import CurrencyItem, ProjectFeeDisplayItem


__all__ = ['ServiceRepository']

logger = logging.getLogger(__name__)


class ServiceRepository:
    """Repository for Service API operations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Service Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = str(settings.services_api.base_url)

    async def list_all(self, token: str) -> list[dict[str, Any]]:
        """
        List all services.

        Returns:
            list[dict[str, Any]]: A list of services

        Raises:
            Exception: If an error occurs while listing services
        """
        url = url_join(self._base_path, 'services-all')
        headers = {'Authorization': f'Bearer {token}'}
        try:
            return (await self._http_client.get(url, headers=headers)).json()

        except Exception as e:
            logger.error('Error listing services: %s', e)
            raise e

    async def get_project_fee_display_options(self, token: str) -> list[ProjectFeeDisplayItem]:
        """
        List all project fee display options.

        Returns:
            list[ProjectFeeDisplayItem]: A list of project fee display options.

        Raises:
            Exception: If an error occurs while listing project fee display options.
        """
        url = url_join(self._base_path, 'project-fee-display')
        headers = {'Authorization': f'Bearer {token}'}
        try:
            response = await self._http_client.get(url, headers=headers)
            response.raise_for_status()
            return [ProjectFeeDisplayItem.model_validate(item) for item in response.json()]
        except Exception as e:
            logger.error('Error listing project fee display options: %s', e)
            raise e

    async def get_currencies(self, token: str) -> list[CurrencyItem]:
        """
        List all currencies.

        Returns:
            list[CurrencyItem]: A list of currencies.

        Raises:
            Exception: If an error occurs while listing currencies.
        """
        url = url_join(self._base_path, 'currencies')
        headers = {'Authorization': f'Bearer {token}'}
        try:
            response = await self._http_client.get(url, headers=headers)
            response.raise_for_status()
            return [CurrencyItem.model_validate(item) for item in response.json()]
        except Exception as e:
            logger.error('Error listing currencies: %s', e)
            raise e
