import logging

from config import settings
from core.http_client import CustomAsyncClient
from core.urls import url_join
from schemas import ProjectRolesData


__all__ = ['RoleRepository']

logger = logging.getLogger(__name__)


class RoleRepository:
    """Repository for Role API operations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Role Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = str(settings.roles_api.base_url)

    async def list(self, token: str) -> list[ProjectRolesData]:
        """
        List all project roles.

        Returns:
            list[ProjectRolesData]: A list of project roles

        Raises:
            Exception: If an error occurs while listing project roles
        """
        url = url_join(self._base_path, 'project-roles')
        headers = {'Authorization': f'Bearer {token}'}

        response = await self._http_client.get(url, headers=headers)
        data = response.json()

        project_roles = [ProjectRolesData(**role) for role in data]
        return project_roles
