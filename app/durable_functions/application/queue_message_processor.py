import json
import logging

import azure.durable_functions as df

from constants.durable_functions import OrchestratorInputType, OrchestratorName
from constants.extracted_data import Tense
from schemas.queue_message import QualQueueMessage, Source


logger = logging.getLogger(__name__)


class OrchestratorInitiator:
    """
    Base class for orchestrator initiators.
    """

    def __init__(self, client: df.DurableOrchestrationClient):
        self.client = client

    async def start_orchestration(self, orchestrator_name: str, orchestrator_input: dict) -> None:
        logger.info(f'Starting orchestrator: {orchestrator_name} with input: {orchestrator_input}')
        instance_id = await self.client.start_new(orchestrator_name, None, orchestrator_input)
        logger.info(f'Started orchestration instance: {instance_id}')


class UnifiedOrchestratorInitiator(OrchestratorInitiator):
    """
    Initiates the unified processing orchestrator.
    """

    async def process(self, source: Source, signalr_user_id: str) -> None:
        orchestrator_input = {
            'text_prompt': str(source.text_prompt) if source.text_prompt else None,
            'documents': [str(doc) for doc in source.documents] if source.documents else None,
            'signalr_user_id': signalr_user_id,
        }
        await self.start_orchestration(OrchestratorName.UnifiedProcessing, orchestrator_input)


class EnhancedExtractionOrchestratorInitiator(OrchestratorInitiator):
    """
    Initiates the enhanced extraction orchestrator.
    """

    async def process(
        self,
        source: Source,
        signalr_user_id: str,
        tense: Tense | None = None,
        confirmed_objective_and_scope: str | None = None,
        confirmed_outcomes: str | None = None,
    ) -> None:
        if not source.message_ids:
            return

        # NOTE: EnhancedProcessingInput will be used for this input validation
        orchestrator_input = {
            'message_ids': [str(message_id) for message_id in source.message_ids],
            'signalr_user_id': signalr_user_id,
            'tense': str(tense) if tense else None,
            'confirmed_objective_and_scope': confirmed_objective_and_scope,
            'confirmed_outcomes': confirmed_outcomes,
        }
        await self.start_orchestration(OrchestratorName.EnchancedExtraction, orchestrator_input)


class DocumentOrchestratorInitiator(OrchestratorInitiator):
    """
    Initiates the document processing orchestrator for individual documents.
    """

    async def process(self, document_url: str, signalr_user_id: str) -> None:
        orchestrator_input = {
            'blob_url': document_url,
            'type': OrchestratorInputType.Document,
            'signalr_user_id': signalr_user_id,
        }
        await self.start_orchestration(OrchestratorName.DocumentProcessing, orchestrator_input)


class PromptOrchestratorInitiator(OrchestratorInitiator):
    """
    Initiates the document processing orchestrator for individual prompts.
    """

    async def process(self, prompt_url: str, signalr_user_id: str) -> None:
        orchestrator_input = {
            'prompt_url': prompt_url,
            'type': OrchestratorInputType.Prompt,
            'signalr_user_id': signalr_user_id,
        }
        await self.start_orchestration(OrchestratorName.DocumentProcessing, orchestrator_input)


class QueueMessageProcessor:
    """
    Processes incoming queue messages and dispatches them to appropriate orchestrators.
    """

    def __init__(self, client: df.DurableOrchestrationClient):
        self.client = client
        self.unified_initiator = UnifiedOrchestratorInitiator(client)
        self.enhanced_initiator = EnhancedExtractionOrchestratorInitiator(client)
        self.document_initiator = DocumentOrchestratorInitiator(client)
        self.prompt_initiator = PromptOrchestratorInitiator(client)

    async def process_message(self, msg_body: str) -> None:
        try:
            msg_json = json.loads(msg_body)
            logger.info(f'Received unified queue message: {msg_json}')

            message = QualQueueMessage.model_validate(msg_json)
            source = message.source
            signalr_user_id = message.signal_r_connection_id
            tense = message.tense
            confirmed_objective_and_scope = message.confirmed_objective_and_scope
            confirmed_outcomes = message.confirmed_outcomes

            if (source.text_prompt and source.documents) or (source.documents and len(source.documents) > 1):
                await self.unified_initiator.process(source, signalr_user_id)
            elif source.message_ids:
                await self.enhanced_initiator.process(
                    source, signalr_user_id, tense, confirmed_objective_and_scope, confirmed_outcomes
                )
            else:
                if source.documents:
                    for document_url in source.documents:
                        await self.document_initiator.process(str(document_url), signalr_user_id)
                if source.text_prompt:
                    await self.prompt_initiator.process(str(source.text_prompt), signalr_user_id)

        except json.JSONDecodeError:
            logger.error(f'Invalid JSON format in queue message: {msg_body}')
        except Exception as e:
            logger.exception(f'Error processing unified queue message: {e}')
