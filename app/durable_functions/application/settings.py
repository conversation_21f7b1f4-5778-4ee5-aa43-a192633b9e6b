from functools import cached_property
import os
from pathlib import Path

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings

from constants.environment import Environment


__all__ = ['Settings']


ENVIRONMENT = Environment(os.environ['ENVIRONMENT'])

with open(Path(__file__).parents[2] / 'version') as fp:
    VERSION = fp.read().strip()


class SignalRSettings(BaseModel):
    CONNECTION_STRING: str
    HUB_NAME: str = 'qualMessageHub'


class BlobStorageSettings(BaseModel):
    CONNECTION_STRING: str
    DEFAULT_CONTAINER_NAME: str
    DOCUMENT_CONTAINER_NAME: str


class QueueSettings(BaseModel):
    CONNECTION_STRING: str

    CONTENT_PROCESSING_QUEUE_NAME: str = 'content-analysis-queue'
    CONTENT_PROCESSING_QUEUE_CHUNKED: str = 'content-analysis-chunked'
    CONTENT_PROCESSING_QUEUE_EXTRACTED: str = 'content-analysis-extracted'

    CONNECTION_ENV: str = 'AZURE_QUEUE_CONNECTION_STRING'


class DocumentIntelligenceSettings(BaseModel):
    ENDPOINT: str
    KEY: str
    MODEL_NAME: str = 'prebuilt-layout'


class ChunkingSettings(BaseModel):
    CHUNK_SIZE: int = 8096
    CHUNK_OVERLAP: int = 512
    ENCODING_NAME: str = 'cl100k_base'
    SEPARATORS: list[str] = ['\n\n', '\n', '. ', ' ', '']


class DatabaseSettings(BaseModel):
    host: str
    port: str
    user: str
    password: str
    name: str
    driver: str

    @cached_property
    def uri(self) -> str:
        return (
            f'mssql+aioodbc:///?odbc_connect=DRIVER={{{self.driver}}};SERVER={self.host},{self.port};'
            f'DATABASE={self.name};UID={self.user};PWD={self.password};'
        )


class OpenAISettings(BaseModel):
    """Settings for Azure OpenAI API."""

    endpoint: str
    key: str
    model: str
    api_version: str
    default_temperature: float


class Settings(BaseSettings):
    SIGNALR_SETTINGS: SignalRSettings
    BLOB_STORAGE_SETTINGS: BlobStorageSettings
    QUEUE_SETTINGS: QueueSettings
    DOCUMENT_INTELLIGENCE_SETTINGS: DocumentIntelligenceSettings
    CHUNKING_SETTINGS: ChunkingSettings = Field(default=ChunkingSettings())
    db: DatabaseSettings
    openai: OpenAISettings
    version: str = VERSION

    class Config:
        env_file = f'{ENVIRONMENT.value}.env'
        env_nested_delimiter = '__'
        extra = 'allow'  # Allow extra fields from environment variables
