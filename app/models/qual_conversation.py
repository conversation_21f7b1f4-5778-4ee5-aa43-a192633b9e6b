import sqlalchemy as sa
from sqlalchemy.orm import relationship

from constants.extracted_data import ConversationState
from core.db import Base


__all__ = ['QualConversation']


class QualConversation(Base):
    __tablename__ = 'QualConversation'

    Id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    PublicId = sa.Column(sa.Uuid, unique=True, nullable=False, server_default=sa.text('NEWID()'))
    QualId = sa.Column(sa.String, nullable=True)
    DashActivityId = sa.Column(sa.Integer, nullable=True)
    IsCompleted = sa.Column(sa.Boolean, nullable=False)
    CreatedAt = sa.Column(sa.DateTime, server_default=sa.func.now(), nullable=False)
    CreatedById = sa.Column(sa.Uuid, nullable=False)
    CreatedByName = sa.Column(sa.String, nullable=True)
    ConfirmedData = sa.Column(sa.UnicodeText, nullable=True)  # JSON field for user-confirmed data
    State = sa.Column(
        sa.Enum(ConversationState, values_callable=lambda x: [e.value for e in x]),
        nullable=False,
        default=ConversationState.INITIAL,
    )

    Messages = relationship('QualConversationMessage', back_populates='Conversation')
    ExtractedData = relationship('QualExtractedData', back_populates='Conversation')
    FieldValues = relationship('QualFieldValue', back_populates='Conversation')
