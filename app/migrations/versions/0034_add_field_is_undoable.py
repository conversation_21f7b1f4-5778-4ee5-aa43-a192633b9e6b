"""add field is undoable

Revision ID: 0034
Revises: 0033
Create Date: 2025-08-15 13:01:41.352821

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0034'
down_revision: Union[str, None] = '0033'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'QualConversationMessage', sa.Column('IsUndoable', sa.Bo<PERSON>an(), server_default=sa.text('0'), nullable=False)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualConversationMessage', 'IsUndoable')
    # ### end Alembic commands ###
