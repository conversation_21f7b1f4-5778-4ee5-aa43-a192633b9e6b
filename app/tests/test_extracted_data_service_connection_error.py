"""
Test for ExtractedDataService connection error handling.
"""

from unittest.mock import AsyncMock
from uuid import UUID

from httpx import Connect<PERSON>rror
import pytest

from constants.extracted_data import DataSourceType
from schemas import ExtractedData
from schemas.extracted_data import IndustryData
from schemas.project_roles import ProjectRolesData
from services import ExtractedDataService


class TestExtractedDataServiceConnectionError:
    """Test connection error handling in ExtractedDataService."""

    @pytest.fixture
    def mock_industry_data_service(self):
        """Mock industry data service that raises ConnectError."""
        service = AsyncMock()
        service.list.side_effect = ConnectError('Connection failed')
        return service

    @pytest.fixture
    def mock_service_data_service(self):
        """Mock service data service that raises ConnectError."""
        service = AsyncMock()
        service.list_all.side_effect = ConnectError('Connection failed')
        return service

    @pytest.fixture
    def mock_role_data_service(self):
        """Mock role data service that raises ConnectError."""
        service = AsyncMock()
        service.list.side_effect = ConnectError('Connection failed')
        return service

    @pytest.fixture
    def mock_ldmf_country_service(self):
        """Mock ldmf country service that raises ConnectError."""
        service = AsyncMock()
        service.list.return_value = [
            {'name': 'Test Country'},
            {'name': 'Another Country'},
            {'name': 'KX Dash Country'},
            {'name': 'Documents Country'},
        ]
        return service

    @pytest.fixture
    def mock_user_info_service(self):
        """Mock user info service that raises ConnectError."""
        service = AsyncMock()
        service.get.side_effect = ConnectError('User info API down')
        return service

    @pytest.fixture
    def extracted_data_service_with_connection_errors(
        self,
        mock_industry_data_service,
        mock_service_data_service,
        mock_role_data_service,
        mock_ldmf_country_service,
        mock_user_info_service,
    ):
        """ExtractedDataService with all external services failing."""
        extracted_data_repository = AsyncMock()
        conversation_repository = AsyncMock()
        quals_clients_repository = AsyncMock()

        return ExtractedDataService(
            extracted_data_repository=extracted_data_repository,
            conversation_repository=conversation_repository,
            quals_clients_repository=quals_clients_repository,
            industry_data_service=mock_industry_data_service,
            role_data_service=mock_role_data_service,
            service_data_service=mock_service_data_service,
            user_info_service=mock_user_info_service,
            ldmf_country_service=mock_ldmf_country_service,
        )

    @pytest.fixture
    def mixed_error_services(self):
        """Services where some succeed and some fail."""
        industry_service = AsyncMock()
        industry_service.list.return_value = {'Technology': {'id': 1, 'name': 'Technology'}}

        service_service = AsyncMock()
        service_service.list_all.side_effect = ConnectError('Service API down')

        role_service = AsyncMock()
        role_service.list.return_value = {
            'Lead Engagement Partner': ProjectRolesData(
                id=1, title='Lead Engagement Partner', order=1, name='Lead Engagement Partner'
            ),
            'LCSP': ProjectRolesData(id=2, title='LCSP', order=2, name='LCSP'),
            'Engagement Manager': ProjectRolesData(
                id=3, title='Engagement Manager', order=3, name='Engagement Manager'
            ),
        }

        return industry_service, service_service, role_service

    @pytest.fixture
    def extracted_data_service_mixed_errors(self, mixed_error_services):
        """ExtractedDataService with mixed success/failure from external services."""
        industry_service, service_service, role_service = mixed_error_services

        extracted_data_repository = AsyncMock()
        conversation_repository = AsyncMock()
        quals_clients_repository = AsyncMock()
        ldmf_country_service = AsyncMock()
        user_info_service = AsyncMock()
        ldmf_country_service.list.return_value = [
            {'name': 'Test Country'},
            {'name': 'Another Country'},
            {'name': 'KX Dash Country'},
            {'name': 'Documents Country'},
        ]
        # Configure user_info_service to return mock user data
        user_info_service.get.return_value = {
            'name': 'Test User',
            'isApprover': False,
            'isContact': False,
        }

        return ExtractedDataService(
            extracted_data_repository=extracted_data_repository,
            conversation_repository=conversation_repository,
            quals_clients_repository=quals_clients_repository,
            industry_data_service=industry_service,
            role_data_service=role_service,
            service_data_service=service_service,
            user_info_service=user_info_service,
            ldmf_country_service=ldmf_country_service,
        )

    async def test_process_activity_data_handles_partial_failures(self, extracted_data_service_mixed_errors):
        """Test that _process_activity_data handles partial failures correctly."""
        # Arrange
        activity_data = {
            'global_industry': 'Technology',
            'global_service': 'Consulting',
            'engagement_lep_emails': ['<EMAIL>'],
        }

        # Act
        result = await extracted_data_service_mixed_errors._process_activity_data(activity_data, token='')

        # Assert
        # Industry should be processed (service succeeded)
        assert 'client_industries' in result
        assert result['client_industries'] == [
            IndustryData(name='Technology', path='', ldmf_country='Global', is_primary=True)
        ]

        # Service should not be processed (service failed)
        assert 'services' not in result or result.get('services') is None

        # Team roles should be processed (role service succeeded)
        expected_role = {'id': 1, 'title': 'Lead Engagement Partner', 'order': 1, 'name': 'Lead Engagement Partner'}
        assert len(result['team_roles']) == 1
        team_member = result['team_roles'][0]
        assert team_member['email'] == '<EMAIL>'
        assert team_member['roles'] == [expected_role]
        assert team_member['name'] == 'Test User'
        assert team_member['is_approver']  # LEP should be approver (default_is_approver=True)
        assert not team_member['is_contact']  # LEP should not be contact (matches mock data)

    async def test_process_activity_data_handles_connection_errors(self, extracted_data_service_with_connection_errors):
        """Test that _process_activity_data handles connection errors gracefully."""
        # Arrange
        activity_data = {
            'global_industry': 'Technology',
            'global_service': 'Consulting',
            'engagement_lep_emails': ['<EMAIL>'],
            'engagement_lcsp_emails': ['<EMAIL>'],
            'engagement_manager_emails': ['<EMAIL>'],
        }

        # Act - this should not raise an exception
        result = await extracted_data_service_with_connection_errors._process_activity_data(activity_data, token='')

        # Assert
        # The method should return the original data with empty team_roles array
        # since external API calls failed
        assert result is not None
        assert isinstance(result, dict)
        assert result['team_roles'] == []  # Should be empty since external calls failed

        # Original data should be preserved
        assert result['global_industry'] == 'Technology'
        assert result['global_service'] == 'Consulting'
        assert result['engagement_lep_emails'] == ['<EMAIL>']

    async def test_update_handles_connection_errors_in_process_activity_data(
        self, extracted_data_service_with_connection_errors
    ):
        """Test that update method handles connection errors in _process_activity_data."""
        # Arrange
        conversation_id = UUID('00000000-0000-0000-0000-000000000001')
        raw_data = {
            'global_industry': 'Technology',
            'global_service': 'Consulting',
            'engagement_lep_emails': ['<EMAIL>'],
        }
        source_type = DataSourceType.KX_DASH

        # Mock the repository to return a new ExtractedData object
        _ = ExtractedData.create(conversation_id=conversation_id, data_source_type=source_type)
        extracted_data_service_with_connection_errors.extracted_data_repository.get.return_value = None
        extracted_data_service_with_connection_errors.extracted_data_repository.update.return_value = None

        # Act - this should not raise an exception
        result = await extracted_data_service_with_connection_errors.update(
            conversation_id, raw_data, source_type, token=''
        )

        # Assert
        assert result is not None
        # Verify that the repository update method was called
        extracted_data_service_with_connection_errors.extracted_data_repository.update.assert_called_once()
