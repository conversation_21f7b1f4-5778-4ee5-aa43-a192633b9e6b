from unittest.mock import AsyncMock, patch

import pytest


__all__ = [
    'mock_service_repo',
]


@pytest.fixture(autouse=True)
def mock_service_repo():
    """Auto-use fixture that mocks ServiceRepository to prevent network calls."""
    with (
        patch(
            'repositories.service.ServiceRepository.get_project_fee_display_options', new_callable=AsyncMock
        ) as mock_project_fee_display_options,
        patch('repositories.service.ServiceRepository.get_currencies', new_callable=AsyncMock) as mock_currencies,
    ):
        mock_project_fee_display_options.return_value = []
        mock_currencies.return_value = []
        yield
