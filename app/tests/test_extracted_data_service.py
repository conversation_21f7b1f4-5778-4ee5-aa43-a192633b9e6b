from datetime import UTC, date, datetime
import json
from unittest.mock import patch
from uuid import UUID

import pytest

from constants.extracted_data import ConversationState, DataSourceType, Tense
from schemas.confirmed_data import ConfirmedData
from schemas.extracted_data import ExtractedData, IndustryData, ServiceData


class TestExtractedDataService:
    async def test_aggregate_data_prioritizes_sources_correctly(
        self,
        extracted_data_service,
        extracted_data_repository,
        kx_dash_extracted_data,
        documents_extracted_data,
        prompt_extracted_data,
    ):
        # Setup repository to return different data for different sources
        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: {
            DataSourceType.KX_DASH: kx_dash_extracted_data,
            DataSourceType.DOCUMENTS: documents_extracted_data,
            DataSourceType.PROMPT: prompt_extracted_data,
        }[data_source_type]

        # Mock the _calculate_project_duration method to return a default value
        with patch.object(extracted_data_service, '_calculate_project_duration', return_value=12):
            # Call the method
            result = await extracted_data_service.aggregate_data(UUID('00000000-0000-0000-0000-000000000001'))

        # Verify the result has merged unique values for client_name, ldmf_country, title
        expected_client_names = sorted(['KX Client', 'Document Client', 'Prompt Client'])
        expected_ldmf_countries = sorted(['KX Country', 'Document Country', 'Prompt Country'])

        assert result.client_name == expected_client_names
        assert result.ldmf_country == expected_ldmf_countries

        # Verify date intervals are collected from all sources
        assert len(result.date_intervals) == 1  # interval dates should return kx dash in priority
        assert ('2023-01-01', '2023-12-31') in result.date_intervals  # KX Dash

        # Verify priority-based fields use highest priority (prompt)
        assert result.objective_and_scope == prompt_extracted_data.objective_and_scope
        assert result.outcomes == prompt_extracted_data.outcomes

    async def test_aggregate_data_fills_missing_prompt_fields_from_other_sources(
        self,
        extracted_data_service,
        extracted_data_repository,
        kx_dash_extracted_data,
        documents_extracted_data,
        prompt_extracted_data,
    ):
        # Make prompt_extracted_data incomplete (missing client_name and ldmf_country)
        prompt_extracted_data.client_name = []
        prompt_extracted_data.ldmf_country = []

        # Setup repository to return different data for different sources
        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: {
            DataSourceType.KX_DASH: kx_dash_extracted_data,
            DataSourceType.DOCUMENTS: documents_extracted_data,
            DataSourceType.PROMPT: prompt_extracted_data,
        }[data_source_type]

        # Mock the _calculate_project_duration method to return a default value
        with patch.object(extracted_data_service, '_calculate_project_duration', return_value=12):
            result = await extracted_data_service.aggregate_data(UUID('00000000-0000-0000-0000-000000000001'))

        # Client names and countries should be merged from all sources (prompt has empty lists)
        expected_client_names = sorted(['KX Client', 'Document Client'])
        expected_ldmf_countries = sorted(['KX Country', 'Document Country'])
        assert result.client_name == expected_client_names
        assert result.ldmf_country == expected_ldmf_countries

    async def test_aggregate_data_uses_other_sources_when_prompt_empty(
        self,
        extracted_data_service,
        extracted_data_repository,
        kx_dash_extracted_data,
        documents_extracted_data,
        prompt_extracted_data,
    ):
        # Make prompt_extracted_data empty
        for field in [
            'activity_name',
            'client_name',
            'ldmf_country',
            'title',
            'start_date',
            'end_date',
            'client_industries',
            'client_services',
            'roles',
            'objective_and_scope',
            'outcomes',
        ]:
            setattr(
                prompt_extracted_data,
                field,
                None if not isinstance(getattr(prompt_extracted_data, field), list) else [],
            )

        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: {
            DataSourceType.KX_DASH: kx_dash_extracted_data,
            DataSourceType.DOCUMENTS: documents_extracted_data,
            DataSourceType.PROMPT: prompt_extracted_data,
        }[data_source_type]

        # Mock the _calculate_project_duration method to return a default value
        with patch.object(extracted_data_service, '_calculate_project_duration', return_value=12):
            result = await extracted_data_service.aggregate_data(UUID('00000000-0000-0000-0000-000000000001'))

        # All fields should be filled from the next available source (documents or kx_dash)
        # Client names and countries should be merged from all sources (prompt is empty)
        expected_client_names = sorted(['KX Client', 'Document Client'])
        expected_ldmf_countries = sorted(['KX Country', 'Document Country'])
        assert result.client_name == expected_client_names
        assert result.ldmf_country == expected_ldmf_countries

    @pytest.mark.parametrize(
        'parametrized_data',
        [
            {
                'document': {
                    'start_date': None,
                    'end_date': date(2025, 12, 31),
                },
                'prompt': {
                    'start_date': date(2025, 1, 1),
                    'end_date': None,
                },
            },
            {
                'document': {
                    'start_date': date(2025, 1, 1),
                    'end_date': None,
                },
                'prompt': {
                    'start_date': None,
                    'end_date': date(2025, 12, 31),
                },
            },
            {
                'document': {
                    'start_date': date(2025, 1, 1),
                    'end_date': date(2025, 12, 31),
                },
                'prompt': {
                    'start_date': date(2025, 2, 1),
                    'end_date': date(2025, 11, 30),
                },
            },
        ],
    )
    async def test_aggregate_data_fills_date_gap_from_following_source(
        self,
        parametrized_data,
        extracted_data_service,
        extracted_data_repository,
    ):
        conversation_id = UUID('00000000-0000-0000-0000-000000000001')

        # Setup repository to return different data for different sources
        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: {
            DataSourceType.DOCUMENTS: ExtractedData(
                ConversationPublicId=conversation_id,
                DataSourceType=DataSourceType.DOCUMENTS,
                CreatedAt=datetime.now(UTC),
                StartDate=parametrized_data['document']['start_date'],
                EndDate=parametrized_data['document']['end_date'],
            ),
            DataSourceType.PROMPT: ExtractedData(
                ConversationPublicId=conversation_id,
                DataSourceType=DataSourceType.PROMPT,
                CreatedAt=datetime.now(UTC),
                StartDate=parametrized_data['prompt']['start_date'],
                EndDate=parametrized_data['prompt']['end_date'],
            ),
        }[data_source_type]

        # Mock the _calculate_project_duration method to return a default value
        with patch.object(extracted_data_service, '_calculate_project_duration', return_value=12):
            result = await extracted_data_service.aggregate_data(conversation_id)

        assert result.date_intervals == [('2025-01-01', '2025-12-31')]

    def test_extracted_data_schema(self):
        source_type = DataSourceType.PROMPT
        conversation_id = UUID('00000000-0000-0000-0000-000000000001')
        extracted = ExtractedData.create(conversation_id=conversation_id, data_source_type=source_type)
        assert extracted.conversation_id == conversation_id
        assert extracted.data_source_type == source_type
        assert extracted.created_at is not None
        model_dump = extracted.model_dump()
        assert model_dump['data_source_type'] == source_type
        assert model_dump['conversation_id'] == conversation_id

        source_type = DataSourceType.KX_DASH
        conversation_id = UUID('00000000-0000-0000-0000-000000000001')
        client_industry = IndustryData(
            name='Test Industry', path='Test Path', ldmf_country='Test Country', is_primary=True
        )
        client_service = ServiceData(name='Test Service', path='Test Path', is_primary=True)
        extracted = ExtractedData.create(conversation_id=conversation_id, data_source_type=source_type)
        extracted.client_industries = [client_industry]
        extracted.client_services = [client_service]
        extracted.roles = json.dumps({'team_members': ['Test Team Member']})

        model_dump = extracted.model_dump()
        assert model_dump['client_services'] == [client_service.model_dump()]
        assert model_dump['client_industries'] == [client_industry.model_dump()]
        assert model_dump['roles'] == json.dumps({'team_members': ['Test Team Member']})
        assert model_dump['conversation_id'] == conversation_id
        assert model_dump['data_source_type'] == source_type

        data = ExtractedData(
            ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
            DataSourceType=DataSourceType.PROMPT,
            CreatedAt=datetime.now(UTC),
            ClientName=json.dumps(['Client1', 'Client2']),  # type: ignore
            LDMFCountry=json.dumps(['Country1']),  # type: ignore
            Roles=json.dumps({'role': 'test'}),
            ActivityName='Test Activity',
            Title='Test Title',
            StartDate=date(2023, 1, 1),
            EndDate=date(2023, 12, 31),
            ClientIndustries=[client_industry],
            ClientServices=[client_service],
            ObjectiveAndScope='Test Objective and Scope',
            Outcomes='Test Outcomes',
            Other='Test Other',
        )

        result = data.model_dump_for_db()

        # Should not contain these fields
        assert 'ConversationPublicId' not in result
        assert 'CreatedAt' not in result

        # Should be JSON strings
        assert result['ClientName'] == json.dumps(['Client1', 'Client2'])
        assert result['LDMFCountry'] == json.dumps(['Country1'])
        assert result['Roles'] == json.dumps({'role': 'test'})
        assert result['ClientIndustries'] == [client_industry.model_dump()]
        assert result['ClientServices'] == [client_service.model_dump()]
        assert result['ObjectiveAndScope'] == 'Test Objective and Scope'
        assert result['Outcomes'] == 'Test Outcomes'
        assert result['ActivityName'] == 'Test Activity'
        assert result['Title'] == 'Test Title'
        assert result['StartDate'] == date(2023, 1, 1)
        assert result['EndDate'] == date(2023, 12, 31)
        assert result['DataSourceType'] == DataSourceType.PROMPT
        assert result['Other'] == 'Test Other'


class TestConfirmedData:
    def test_from_json_string_with_none_returns_empty(self):
        data = ConfirmedData.from_json_string(None)
        assert isinstance(data, ConfirmedData)
        assert data.is_empty

    def test_from_json_string_with_invalid_json_returns_empty(self):
        data = ConfirmedData.from_json_string('not a json')
        assert isinstance(data, ConfirmedData)
        assert data.is_empty

    def test_from_json_string_with_valid_json(self):
        json_str = json.dumps(
            {
                'client_name': 'Acme Corp',
                'ldmf_country': 'USA',
                'date_intervals': ['2023-01-01', '2023-12-31'],
                'objective_and_scope': 'Test objective',
                'outcomes': 'Test outcomes',
            }
        )
        data = ConfirmedData.from_json_string(json_str)
        assert data.client_name == 'Acme Corp'
        assert data.ldmf_country == 'USA'
        assert data.date_intervals == ('2023-01-01', '2023-12-31')
        assert data.objective_and_scope == 'Test objective'
        assert data.outcomes == 'Test outcomes'

    def test_to_json_string_excludes_none_fields(self):
        data = ConfirmedData(client_name='Acme', ldmf_country=None)
        json_str = data.to_json_string()
        assert 'client_name' in json_str
        assert 'ldmf_country' not in json_str

    def test_required_fields_are_complete_true(self):
        data = ConfirmedData(
            client_name='Acme',
            ldmf_country='USA',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Scope',
            outcomes='Outcome',
        )
        assert data.required_fields_are_complete

    def test_required_fields_are_complete_false(self):
        data = ConfirmedData(
            client_name='',
            ldmf_country='USA',
            date_intervals=('2023-01-01', None),
            objective_and_scope='Scope',
            outcomes='Outcome',
        )
        assert not data.required_fields_are_complete

    def test_is_empty_true(self):
        data = ConfirmedData()
        assert data.is_empty

    def test_is_empty_false(self):
        data = ConfirmedData(client_name='Acme')
        assert not data.is_empty

    @pytest.mark.parametrize(
        'fields,expected_state',
        [
            ({}, ConversationState.COLLECTING_CLIENT_NAME),
            ({'client_name': 'Acme'}, ConversationState.COLLECTING_COUNTRY),
            ({'client_name': 'Acme', 'ldmf_country': 'USA'}, ConversationState.COLLECTING_DATES),
            (
                {'client_name': 'Acme', 'ldmf_country': 'USA', 'date_intervals': ('2023-01-01', '2023-12-31')},
                ConversationState.COLLECTING_OBJECTIVE,
            ),
            (
                {
                    'client_name': 'Acme',
                    'ldmf_country': 'USA',
                    'date_intervals': ('2023-01-01', '2023-12-31'),
                    'objective_and_scope': 'Scope',
                },
                ConversationState.COLLECTING_OUTCOMES,
            ),
            (
                {
                    'client_name': 'Acme',
                    'ldmf_country': 'USA',
                    'date_intervals': ('2023-01-01', '2023-12-31'),
                    'objective_and_scope': 'Scope',
                    'outcomes': 'Outcome',
                },
                ConversationState.DATA_COMPLETE,
            ),
        ],
    )
    def test_get_current_conversation_state(self, fields, expected_state):
        data = ConfirmedData(**fields)
        assert data.get_current_conversation_state() == expected_state

    @pytest.mark.parametrize(
        'date_intervals,expected_tense',
        [
            (None, Tense.PAST),
            (('2023-01-01', None), Tense.PAST),
            (('2023-01-01', '2023-12-31'), Tense.PAST),
            (('2023-01-01', '2025-12-31'), Tense.PRESENT),
        ],
    )
    def test_tense_type(self, date_intervals, expected_tense):
        data = ConfirmedData(date_intervals=date_intervals)
        assert data.tense == expected_tense
