from unittest.mock import AsyncMock, patch
from uuid import uuid4

from fastapi import status
import pytest

from constants.extracted_data import ConversationState
from constants.message import (
    THANX_FOR_INFORMATION,
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    SystemReplyType,
)
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from core.urls import URLResolver
from schemas import AggregatedData
from schemas.confirmed_data import ConfirmedData
from schemas.conversation_message.message import MessageValidator, SystemMessageSerializer, UserMessageSerializer
from schemas.quals_clients import ClientSearchResponse


class TestGetLastMessageRefactored:
    """Test suite for the refactored GET last message endpoint with system message generation."""

    @pytest.fixture
    def user_message_data(self, test_conversation_id):
        """Create sample user message data."""
        return {
            'id': uuid4(),
            'conversation_id': test_conversation_id,
            'role': MessageRole.USER,
            'type': MessageType.TEXT,
            'content': 'Test user message',
            'created_at': '2024-01-01T00:00:00Z',
            'selected_option': None,
            'system_reply_type': None,
        }

    @pytest.fixture
    def system_message_data(self, test_conversation_id):
        """Create sample system message data."""
        return {
            'id': uuid4(),
            'conversation_id': test_conversation_id,
            'role': MessageRole.SYSTEM,
            'type': MessageType.TEXT,
            'content': 'Test system message',
            'created_at': '2024-01-01T00:00:00Z',
            'options': '[]',
            'suggested_prompts': '[]',
            'system_reply_type': None,
        }

    @pytest.fixture
    def sample_aggregated_data(self):
        """Create sample aggregated data for testing."""
        return AggregatedData(
            client_name=['Test Client Inc.'],
            ldmf_country=['United States'],
            date_intervals=[('2024-01-01', '2024-12-31')],
            date_intervals_original=[('2024-01-01', '2024-12-31')],
            objective_and_scope='Test objective and scope content',
            outcomes='Test outcomes content',
        )

    @pytest.fixture
    def empty_aggregated_data(self):
        """Create empty aggregated data for testing."""
        return AggregatedData(
            client_name=[],
            ldmf_country=[],
            date_intervals=[],
            date_intervals_original=[],
            objective_and_scope=None,
            outcomes=None,
        )

    async def test_get_last_message_system_message_exists(
        self,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_mock,
        auth_header,
        test_conversation_id,
        system_message_data,
    ):
        """Test getting last message when it's already a system message."""
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Mock the message service to return a system message
        with patch('services.conversation_message.ConversationMessageService.get_last') as mock_get_last:
            mock_get_last.return_value = SystemMessageSerializer.model_validate(system_message_data)

            response = await async_client.get(url, headers=auth_header)

        assert response.status_code == status.HTTP_200_OK, response.json()
        data = response.json()
        assert data['role'] == MessageRole.SYSTEM
        assert data['content'] == 'Test system message'

    async def test_get_last_message_user_message_with_extracted_data(
        self,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_mock,
        auth_header,
        test_conversation_id,
        sample_aggregated_data,
    ):
        """Test getting last message when it's a user message and extracted data exists."""
        # post new user message
        user_message = 'I need help with my qual for NewTech Solutions'

        with (
            patch(
                'services.message_processor.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
            patch('services.ldmf_country.LDMFCountryService.list', new_callable=AsyncMock) as mock_list,
            patch('services.client_industry.ClientIndustryDataService.list') as mock_industry_list,
            patch('services.client_service.ClientServiceDataService.list') as mock_service_list,
        ):
            # Set up mocks for first message
            mock_conversation = type(
                'MockConversation',
                (),
                {'id': test_conversation_id, 'PublicId': test_conversation_id, 'State': 'collecting_client_name'},
            )
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock no client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
                exact_match=False,
            )

            # Mock LDMF country service
            mock_list.return_value = {}

            # Mock industry list to return empty list
            mock_industry_list.return_value = []

            # Mock service list to return empty list
            mock_service_list.return_value = []

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        assert message_response.status_code == status.HTTP_201_CREATED, message_response.json()
        data = message_response.json()

        assert 'system' in data
        assert data['system'] is None

        # test get last
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Mock the services
        with (
            patch('services.extracted_data.ExtractedDataService.aggregate_data') as mock_aggregate_data,
            patch('services.ldmf_country.LDMFCountryService.verify_ldmf_country') as mock_verify_ldmf_country,
            patch(
                'services.message_handlers.prompt_handler.PromptMessageHandler._is_date_unambiguous_and_complete'
            ) as mock_is_date_unambiguous_and_complete,
        ):
            # Setup extracted data service mock
            mock_aggregate_data.return_value = sample_aggregated_data
            mock_is_date_unambiguous_and_complete.return_value = True

            mock_verify_country = lambda *args, **kwargs: ['Test Country']
            mock_verify_ldmf_country.side_effect = mock_verify_country

            response = await async_client.get(url, headers=auth_header)

        assert response.status_code == status.HTTP_200_OK, response.json()
        data = response.json()
        assert data['role'] == MessageRole.SYSTEM
        assert data['content'] == SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION.message_text.format(
            client_name=sample_aggregated_data.client_name[0]
        )

        # Verify the services were called correctly
        mock_aggregate_data.assert_called_once_with(test_conversation_id)

    async def test_get_last_message_user_message_no_extracted_data(
        self,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_mock,
        auth_header,
        test_conversation_id,
        empty_aggregated_data,
    ):
        """Test getting last message when it's a user message but no extracted data exists."""
        # post new user message
        user_message = 'I need help with my qual for NewTech Solutions'

        with (
            patch(
                'services.message_processor.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
            patch('services.ldmf_country.LDMFCountryService.list', new_callable=AsyncMock) as mock_list,
            patch('services.client_industry.ClientIndustryDataService.list') as mock_industry_list,
            patch('services.client_service.ClientServiceDataService.list') as mock_service_list,
        ):
            # Set up mocks for first message
            mock_conversation = type(
                'MockConversation',
                (),
                {'id': test_conversation_id, 'PublicId': test_conversation_id, 'State': 'collecting_client_name'},
            )
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock no client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
                exact_match=False,
            )

            # Mock LDMF country service
            mock_list.return_value = {}

            # Mock industry list
            mock_industry_list.return_value = []

            # Mock service list
            mock_service_list.return_value = []

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        assert message_response.status_code == status.HTTP_201_CREATED, message_response.json()
        data = message_response.json()

        assert 'system' in data
        assert data['system'] is None

        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Mock the services
        with (
            patch('services.extracted_data.ExtractedDataService.aggregate_data') as mock_aggregate_data,
            patch('services.ldmf_country.LDMFCountryService.verify_ldmf_country') as mock_verify_ldmf_country,
        ):
            # Setup extracted data service mock (returns empty data)
            mock_aggregate_data.return_value = empty_aggregated_data

            mock_verify_country = lambda *args, **kwargs: None
            mock_verify_ldmf_country.side_effect = mock_verify_country

            response = await async_client.get(url, headers=auth_header)

        assert response.status_code == status.HTTP_200_OK, response.json()
        data = response.json()
        assert data['role'] == MessageRole.SYSTEM
        assert data['content'] == SystemReplyType.NEED_INFO_INITIAL_MISSING.message_text

        # Verify extracted data service was called but no system message was created
        mock_aggregate_data.assert_called_with(test_conversation_id)

    async def test_get_last_message_user_message_text_type(
        self,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_mock,
        auth_header,
        test_conversation_id,
        sample_aggregated_data,
    ):
        """Test getting last message when it's a user message with file type."""
        # post new user message
        user_message = 'I need help with my qual for NewTech Solutions'

        with (
            patch(
                'services.message_processor.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
            patch('services.ldmf_country.LDMFCountryService.list', new_callable=AsyncMock) as mock_list,
            patch('services.client_industry.ClientIndustryDataService.list') as mock_industry_list,
            patch('services.client_service.ClientServiceDataService.list') as mock_service_list,
        ):
            # Set up mocks for first message
            mock_conversation = type(
                'MockConversation',
                (),
                {'id': test_conversation_id, 'PublicId': test_conversation_id, 'State': 'collecting_client_name'},
            )
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock no client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
                exact_match=False,
            )

            # Mock LDMF country service
            mock_list.return_value = {}

            # Mock industry list
            mock_industry_list.return_value = []

            # Mock service list
            mock_service_list.return_value = []

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        assert message_response.status_code == status.HTTP_201_CREATED, message_response.json()
        data = message_response.json()

        assert 'system' in data
        assert data['system'] is None

        # Test last message with mocked aggregated data
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Mock the services
        with (
            patch('services.extracted_data.ExtractedDataService.aggregate_data') as mock_aggregate_data,
            patch('services.ldmf_country.LDMFCountryService.verify_ldmf_country') as mock_verify_ldmf_country,
            patch(
                'services.message_handlers.prompt_handler.PromptMessageHandler._is_date_unambiguous_and_complete'
            ) as mock_is_date_unambiguous_and_complete,
        ):
            # Setup other service mocks
            mock_aggregate_data.return_value = sample_aggregated_data
            mock_is_date_unambiguous_and_complete.return_value = True

            mock_verify_country = lambda *args, **kwargs: ['Test Country']
            mock_verify_ldmf_country.side_effect = mock_verify_country

            response = await async_client.get(url, headers=auth_header)

            assert response.status_code == status.HTTP_200_OK, response.json()
            data = response.json()
            assert data['role'] == MessageRole.SYSTEM
            assert data['type'] == MessageType.TEXT
            assert data['content'] == SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION.message_text.format(
                client_name=sample_aggregated_data.client_name[0]
            )

    async def test_get_last_message_system_generation_error(
        self,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_mock,
        auth_header,
        test_conversation_id,
        user_message_data,
    ):
        """Test getting last message when system message generation fails."""
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Mock the services with error in system message generation
        with (
            patch('services.conversation_message.ConversationMessageService.get_last') as mock_get_last,
            patch('services.extracted_data.ExtractedDataService.aggregate_data') as mock_aggregate_data,
        ):
            # Setup message service mock
            mock_get_last.return_value = UserMessageSerializer.model_validate(user_message_data)

            # Setup extracted data service mock to raise an error
            mock_aggregate_data.side_effect = Exception('Test error')

            response = await async_client.get(url, headers=auth_header)

        # Should fall back to returning the original user message
        assert response.status_code == status.HTTP_200_OK, response.json()
        data = response.json()
        assert data['role'] == MessageRole.USER
        assert data['content'] == 'Test user message'

    @patch('services.conversation_message.ExtractedDataService.aggregate_data')
    @patch('repositories.conversation.ConversationRepository.get_confirmed_data')
    @patch('repositories.conversation.ConversationRepository.update_confirmed_data_and_state')
    @patch('services.ldmf_country.LDMFCountryService.verify_ldmf_country')
    async def test_get_last_message_confirmed_data_update_logic(
        self,
        mock_verify_ldmf_country,
        mock_update_confirmed_data,
        mock_get_confirmed_data,
        mock_aggregate_data,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_header: dict,
        conversation_message_repository_dep_with_autocommit,
        user_message_data: dict,
        test_conversation_id,
    ):
        """Test that confirmed data is updated correctly when aggregated data is complete and confirmed data is empty."""
        # Setup URL
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Create user message in database
        message_validator = MessageValidator.model_validate(user_message_data)
        user_message = await conversation_message_repository_dep_with_autocommit.create(message_validator)

        # Mock empty confirmed data
        empty_confirmed_data = ConfirmedData()
        mock_get_confirmed_data.return_value = empty_confirmed_data

        # Mock complete aggregated data with single values (using unambiguous dates)
        complete_aggregated_data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['United States'],
            date_intervals=[('2024-01-15', '2024-12-20')],  # Both days > 12, unambiguous
            date_intervals_original=[('2024-01-15', '2024-12-20')],
            objective_and_scope='Test objective and scope',
            outcomes='Test outcomes',
        )
        mock_aggregate_data.return_value = complete_aggregated_data

        # Mock LDMF country verification to return the country name
        mock_verify_country = lambda *args, **kwargs: ['United States']
        mock_verify_ldmf_country.side_effect = mock_verify_country

        with patch('repositories.conversation_message.ConversationMessageRepository.get_last') as mock_get_last:
            mock_get_last.return_value = user_message

            response = await async_client.get(url, headers=auth_header)

        # Verify response
        assert response.status_code == status.HTTP_200_OK, response.json()

        # Verify that update_confirmed_data_and_state was called
        mock_update_confirmed_data.assert_called_once()

        # Get the call arguments
        call_args = mock_update_confirmed_data.call_args
        assert call_args[1]['public_id'] == test_conversation_id
        assert call_args[1]['state'] == ConversationState.COLLECTING_CLIENT_NAME

        # Verify confirmed data was updated with all fields except client_name
        updated_confirmed_data = call_args[1]['confirmed_data']
        assert updated_confirmed_data.client_name is None  # Should not be set
        assert updated_confirmed_data.ldmf_country == complete_aggregated_data.ldmf_country[0]
        assert updated_confirmed_data.date_intervals == complete_aggregated_data.date_intervals[0]
        assert updated_confirmed_data.objective_and_scope == complete_aggregated_data.objective_and_scope
        assert updated_confirmed_data.outcomes == complete_aggregated_data.outcomes

    @patch('services.conversation_message.ExtractedDataService.aggregate_data')
    @patch('repositories.conversation.ConversationRepository.get_confirmed_data')
    @patch('repositories.conversation.ConversationRepository.update_confirmed_data_and_state')
    @patch('services.ldmf_country.LDMFCountryService.verify_ldmf_country')
    async def test_get_last_message_more_than_two_dates_field_prevents_auto_confirmation(
        self,
        mock_verify_ldmf_country,
        mock_update_confirmed_data,
        mock_get_confirmed_data,
        mock_aggregate_data,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_header: dict,
        conversation_message_repository_dep_with_autocommit,
        user_message_data: dict,
        test_conversation_id,
    ):
        """Test that when more_than_two_dates=True, the system prevents auto-confirmation of dates
        and shows a date picker instead of auto-confirming the first date interval."""

        # Setup URL
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Create user message in database
        message_validator = MessageValidator.model_validate(user_message_data)
        user_message = await conversation_message_repository_dep_with_autocommit.create(message_validator)

        # Mock empty confirmed data
        empty_confirmed_data = ConfirmedData()
        mock_get_confirmed_data.return_value = empty_confirmed_data

        # Mock aggregated data with multiple dates and more_than_two_dates flag set to True
        aggregated_data_with_more_than_two_dates = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['United States'],
            date_intervals=[
                ('2024-03-15', '2024-05-20'),  # Days > 12, unambiguous
                ('2024-06-25', '2024-08-30'),  # Days > 12, unambiguous
                ('2024-09-18', '2024-11-22'),  # Days > 12, unambiguous
            ],
            date_intervals_original=[
                ('2024-03-15', '2024-05-20'),
                ('2024-06-25', '2024-08-30'),
                ('2024-09-18', '2024-11-22'),
            ],
            objective_and_scope='Test objective and scope',
            outcomes='Test outcomes',
            more_than_two_dates=True,  # This should prevent auto-confirmation
        )
        mock_aggregate_data.return_value = aggregated_data_with_more_than_two_dates

        # Mock LDMF country verification to return the country name
        mock_verify_country = lambda *args, **kwargs: ['United States']
        mock_verify_ldmf_country.side_effect = mock_verify_country

        with patch('repositories.conversation_message.ConversationMessageRepository.get_last') as mock_get_last:
            mock_get_last.return_value = user_message

            response = await async_client.get(url, headers=auth_header)

        # Verify response
        assert response.status_code == status.HTTP_200_OK, response.json()

        # Verify that update_confirmed_data_and_state was called
        mock_update_confirmed_data.assert_called_once()

        # Get the call arguments
        call_args = mock_update_confirmed_data.call_args
        assert call_args[1]['public_id'] == test_conversation_id

        # Verify confirmed data was updated with all fields EXCEPT date_intervals
        # because more_than_two_dates=True should prevent auto-confirmation of dates
        updated_confirmed_data = call_args[1]['confirmed_data']
        assert updated_confirmed_data.client_name is None  # Should not be auto-confirmed
        assert updated_confirmed_data.ldmf_country == 'United States'  # Should be auto-confirmed
        assert (
            updated_confirmed_data.date_intervals is None
        )  # Should NOT be auto-confirmed due to more_than_two_dates=True
        assert updated_confirmed_data.objective_and_scope == 'Test objective and scope'
        assert updated_confirmed_data.outcomes == 'Test outcomes'

        # Verify the response data contains the expected system message for multiple dates
        response_data = response.json()
        assert response_data['role'] == MessageRole.SYSTEM
        assert response_data['content'] == SystemReplyType.DATES_AMBIGUOUS.message_text

    @patch('services.conversation_message.ExtractedDataService.aggregate_data')
    @patch('repositories.conversation.ConversationRepository.get_confirmed_data')
    @patch('repositories.conversation.ConversationRepository.update_confirmed_data_and_state')
    @patch('services.ldmf_country.LDMFCountryService.verify_ldmf_country')
    async def test_get_last_message_confirmed_data_partially_filled(
        self,
        mock_verify_ldmf_country,
        mock_update_confirmed_data,
        mock_get_confirmed_data,
        mock_aggregate_data,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_header: dict,
        conversation_message_repository_dep_with_autocommit,
        user_message_data: dict,
        test_conversation_id,
    ):
        """Test update logic when confirmed data is partially filled."""
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Create user message in database
        message_validator = MessageValidator.model_validate(user_message_data)
        user_message = await conversation_message_repository_dep_with_autocommit.create(message_validator)

        # Confirmed data with only ldmf_country filled
        partial_confirmed_data = ConfirmedData(ldmf_country='Canada')
        mock_get_confirmed_data.return_value = partial_confirmed_data

        # Aggregated data with all fields (using unambiguous dates)
        complete_aggregated_data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['Canada'],
            date_intervals=[('2024-01-15', '2024-12-20')],  # Both days > 12, unambiguous
            date_intervals_original=[('2024-01-15', '2024-12-20')],
            objective_and_scope='Test objective and scope',
            outcomes='Test outcomes',
        )
        mock_aggregate_data.return_value = complete_aggregated_data

        # Mock LDMF country verification to return the country name
        mock_verify_country = lambda *args, **kwargs: ['Canada']
        mock_verify_ldmf_country.side_effect = mock_verify_country

        with (
            patch('repositories.conversation_message.ConversationMessageRepository.get_last') as mock_get_last,
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
        ):
            mock_get_last.return_value = user_message
            # Set up mocks
            mock_conversation = type(
                'MockConversation',
                (),
                {'id': test_conversation_id, 'PublicId': test_conversation_id, 'State': 'collecting_client_name'},
            )
            mock_get_conversation.return_value = mock_conversation
            response = await async_client.get(url, headers=auth_header)

        assert response.status_code == status.HTTP_200_OK, response.json()
        mock_update_confirmed_data.assert_called_once()
        call_args = mock_update_confirmed_data.call_args
        updated_confirmed_data = call_args[1]['confirmed_data']
        # ldmf_country should remain as 'Canada', others should be updated
        assert updated_confirmed_data.ldmf_country == partial_confirmed_data.ldmf_country
        assert updated_confirmed_data.date_intervals == complete_aggregated_data.date_intervals[0]
        assert updated_confirmed_data.objective_and_scope == complete_aggregated_data.objective_and_scope
        assert updated_confirmed_data.outcomes == complete_aggregated_data.outcomes

        data = response.json()
        assert data['role'] == MessageRole.SYSTEM
        assert data['content'] == SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION.message_text.format(
            client_name=complete_aggregated_data.client_name[0]
        )

    @patch('services.conversation_message.ExtractedDataService.aggregate_data')
    @patch('repositories.conversation.ConversationRepository.get_confirmed_data')
    @patch('repositories.conversation.ConversationRepository.update_confirmed_data_and_state')
    @patch('services.ldmf_country.LDMFCountryService.verify_ldmf_country')
    async def test_get_last_message_confirmed_data_fully_filled(
        self,
        mock_verify_ldmf_country,
        mock_update_confirmed_data,
        mock_get_confirmed_data,
        mock_aggregate_data,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_header: dict,
        conversation_message_repository_dep_with_autocommit,
        user_message_data: dict,
        test_conversation_id,
    ):
        """Test update logic when confirmed data is already fully filled."""
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Create user message in database
        message_validator = MessageValidator.model_validate(user_message_data)
        user_message = await conversation_message_repository_dep_with_autocommit.create(message_validator)

        # Fully filled confirmed data
        full_confirmed_data = ConfirmedData(
            ldmf_country='Mexico',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Confirmed scope',
            outcomes='Confirmed outcomes',
        )
        mock_get_confirmed_data.return_value = full_confirmed_data

        # Aggregated data with different values
        complete_aggregated_data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['USA'],
            date_intervals=[('2024-01-01', '2024-12-31')],
            date_intervals_original=[('2024-01-01', '2024-12-31')],
            objective_and_scope='Aggregated scope',
            outcomes='Aggregated outcomes',
        )
        mock_aggregate_data.return_value = complete_aggregated_data

        with (
            patch('repositories.conversation_message.ConversationMessageRepository.get_last') as mock_get_last,
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
        ):
            mock_get_last.return_value = user_message
            # Set up mocks
            mock_conversation = type(
                'MockConversation',
                (),
                {'id': test_conversation_id, 'PublicId': test_conversation_id, 'State': 'collecting_client_name'},
            )
            mock_get_conversation.return_value = mock_conversation
            response = await async_client.get(url, headers=auth_header)

        assert response.status_code == status.HTTP_200_OK, response.json()
        # Should not update confirmed data if already filled
        mock_update_confirmed_data.assert_not_called()
        data = response.json()
        assert data['role'] == MessageRole.SYSTEM
        assert data['content'] == SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION.message_text.format(
            client_name=complete_aggregated_data.client_name[0]
        )

    @patch('services.conversation_message.ExtractedDataService.aggregate_data')
    @patch('repositories.conversation.ConversationRepository.get_confirmed_data')
    @patch('services.ldmf_country.LDMFCountryService.verify_ldmf_country')
    async def test_get_last_message_aggregated_data_partially_filled(
        self,
        mock_verify_ldmf_country,
        mock_get_confirmed_data,
        mock_aggregate_data,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_header: dict,
        conversation_message_repository_dep_with_autocommit,
        user_message_data: dict,
        test_conversation_id,
    ):
        """Test update logic when aggregated data is partially filled."""
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)
        # Create user message in database
        message_validator = MessageValidator.model_validate(user_message_data)
        user_message = await conversation_message_repository_dep_with_autocommit.create(message_validator)

        # Empty confirmed data
        mock_get_confirmed_data.return_value = ConfirmedData()

        # Aggregated data with only ldmf_country and outcomes
        partial_aggregated_data = AggregatedData(
            client_name=[],
            ldmf_country=['France'],
            date_intervals=[],
            date_intervals_original=[],
            objective_and_scope=None,
            outcomes='Partial outcome',
        )
        mock_aggregate_data.return_value = partial_aggregated_data

        # Mock LDMF country verification to return the country name
        mock_verify_country = lambda *args, **kwargs: [
            'France',
        ]
        mock_verify_ldmf_country.side_effect = mock_verify_country

        with patch('repositories.conversation_message.ConversationMessageRepository.get_last') as mock_get_last:
            mock_get_last.return_value = user_message
            response = await async_client.get(url, headers=auth_header)

        assert response.status_code == status.HTTP_200_OK, response.json()
        data = response.json()
        assert data['role'] == MessageRole.SYSTEM
        assert data['content'] == f'{THANX_FOR_INFORMATION}\n{SystemReplyType.NEED_INFO_CLIENT_NAME.message_text}'

    @patch('services.conversation_message.ExtractedDataService.aggregate_data')
    @patch('repositories.conversation.ConversationRepository.get_confirmed_data')
    @patch('repositories.conversation.ConversationRepository.update_confirmed_data_and_state')
    @patch('services.ldmf_country.LDMFCountryService.verify_ldmf_country')
    async def test_get_last_message_aggregated_data_fully_filled(
        self,
        mock_verify_ldmf_country,
        mock_update_confirmed_data,
        mock_get_confirmed_data,
        mock_aggregate_data,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_header: dict,
        conversation_message_repository_dep_with_autocommit,
        user_message_data: dict,
        test_conversation_id,
    ):
        """Test update logic when aggregated data is fully filled and confirmed data is empty."""
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Create user message in database
        message_validator = MessageValidator.model_validate(user_message_data)
        user_message = await conversation_message_repository_dep_with_autocommit.create(message_validator)

        # Empty confirmed data
        mock_get_confirmed_data.return_value = ConfirmedData()

        # Fully filled aggregated data (using unambiguous dates)
        full_aggregated_data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['Germany'],
            date_intervals=[('2022-01-15', '2022-12-20')],  # Both days > 12, unambiguous
            date_intervals_original=[('2022-01-15', '2022-12-20')],
            objective_and_scope='Full scope',
            outcomes='Full outcomes',
        )
        mock_aggregate_data.return_value = full_aggregated_data

        # Mock LDMF country verification to return the country name
        mock_verify_country = lambda *args, **kwargs: ['Germany']
        mock_verify_ldmf_country.side_effect = mock_verify_country

        with patch('repositories.conversation_message.ConversationMessageRepository.get_last') as mock_get_last:
            mock_get_last.return_value = user_message
            response = await async_client.get(url, headers=auth_header)

        assert response.status_code == status.HTTP_200_OK, response.json()
        mock_update_confirmed_data.assert_called_once()
        call_args = mock_update_confirmed_data.call_args
        updated_confirmed_data = call_args[1]['confirmed_data']
        assert updated_confirmed_data.ldmf_country == full_aggregated_data.ldmf_country[0]
        assert updated_confirmed_data.date_intervals == full_aggregated_data.date_intervals[0]
        assert updated_confirmed_data.objective_and_scope == full_aggregated_data.objective_and_scope
        assert updated_confirmed_data.outcomes == full_aggregated_data.outcomes
