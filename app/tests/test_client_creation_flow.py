"""
Tests for client creation flow when no existing client is found.
"""

from unittest.mock import AsyncMock, patch

from fastapi import status

from constants.durable_functions import ProcessingStatus
from constants.extracted_data import ConversationState
from constants.message import (
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    SuggestedUserPrompt,
)
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from repositories import ConversationMessageRepository, ProcessingMessageRepository
from schemas import ClientSearchResponse, ConfirmedData, MessageValidator, ProcessingStatusUpdatePayload
from schemas.quals_clients import ClientComprehensive, ClientCreateResponse


class TestClientCreationFlow:
    """Test client creation flow for new clients."""

    async def test_client_creation_confirmation_flow(
        self,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
        conversation_message_repository_dep_with_autocommit: ConversationMessageRepository,
        processing_message_repository_dep_with_autocommit: ProcessingMessageRepository,
    ):
        """
        Test the complete flow: no client found -> ask to create -> user confirms -> client created.
        """

        # Step 1: User provides unknown client name
        user_message = 'I need help with my qual for NewTech Solutions'

        with (
            patch('services.message_processor.ConversationMessageProcessor._get_intent') as mock_get_intent,
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.conversation.ConversationRepository.update_state') as mock_update_state,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
            patch('services.ldmf_country.LDMFCountryService.list') as mock_ldmf_list,
            patch('services.client_industry.ClientIndustryDataService.list') as mock_industry_list,
            patch('services.client_service.ClientServiceDataService.list') as mock_service_list,
        ):
            # Set up mocks for first message
            mock_get_intent.return_value = ConversationMessageIntention.EXTRACTION
            mock_conversation = type(
                'MockConversation', (), {'id': test_conversation_id, 'State': 'collecting_client_name'}
            )
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock no client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
                exact_match=False,
            )

            # Mock LDMF country service to return empty countries dict
            mock_ldmf_list.return_value = {}

            # Mock industry list to return empty list
            mock_industry_list.return_value = []

            # Mock service list to return empty list
            mock_service_list.return_value = []

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify first response - should ask if user wants to add as new client
        assert message_response.status_code == status.HTTP_201_CREATED, message_response.json()
        message_data = message_response.json()

        assert 'system' in message_data
        assert message_data['system'] is None

        # Mock completed status for the message
        await processing_message_repository_dep_with_autocommit.create(
            ProcessingStatusUpdatePayload(
                message_id=message_data['user']['id'],
                message=None,
                metadata=None,
                status=ProcessingStatus.DocumentExtractionCompleted,
            )
        )

        # Create system message that should be created by durable function after extraction.
        await conversation_message_repository_dep_with_autocommit.create(
            MessageValidator(
                conversation_id=test_conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.FILE,
                content='',
                system_reply_type=None,
                options=[],
                suggested_prompts=[],
            )
        )

        # Verify conversation state was updated and proposed client name was stored
        mock_update_state.assert_called_with(test_conversation_id, ConversationState.COLLECTING_CLIENT_NAME)

        # Step 2: User confirms they want to add the new client
        confirmation_message = SuggestedUserPrompt.YES.value

        with (
            patch(
                'services.message_processor.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation2,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data2,
            patch('repositories.quals_clients.QualsClientsRepository.create_client') as mock_create_client,
            patch(
                'services.extracted_data.service.ExtractedDataService.update_confirmed_data'
            ) as mock_update_confirmed,
            patch('services.ldmf_country.LDMFCountryService.list') as mock_ldmf_list2,
            patch('services.client_industry.ClientIndustryDataService.list') as mock_industry_list2,
            patch('services.client_service.ClientServiceDataService.list') as mock_service_list2,
        ):
            # Set up mocks for confirmation message
            mock_get_conversation2.return_value = type(
                'MockConversation', (), {'id': test_conversation_id, 'State': ConversationState.COLLECTING_CLIENT_NAME}
            )

            # Mock confirmed data with proposed client name
            confirmed_data_with_proposed = ConfirmedData(proposed_client_name='NewTech Solutions')
            mock_get_confirmed_data2.return_value = confirmed_data_with_proposed

            # Mock successful client creation
            mock_client = ClientComprehensive(
                id=101,
                name='NewTech Solutions',
                description='Technology consulting company',
                primaryLocalIndustry='Technology',
                primaryGlobalIndustry='Information Technology',
            )
            mock_create_client.return_value = ClientCreateResponse(
                client=mock_client, success=True, message='Client created successfully'
            )

            # Mock LDMF country service to return empty countries dict
            mock_ldmf_list2.return_value = {}

            # Mock industry list to return empty list
            mock_industry_list2.return_value = []

            # Mock service list to return empty list
            mock_service_list2.return_value = []

            confirmation_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': confirmation_message,
            }
            confirmation_response = await async_client.post(message_url, headers=auth_header, data=confirmation_data)

        # Verify confirmation response - should confirm client creation
        assert confirmation_response.status_code == status.HTTP_201_CREATED, confirmation_response.json()
        confirmation_data = confirmation_response.json()

        assert 'system' in confirmation_data
        assert confirmation_data['system'] is None

        # Verify client was created and confirmed data was updated
        mock_update_confirmed.assert_called_once_with(
            conversation_id=test_conversation_id,
            field_name='client_name',
            field_value='NewTech Solutions',
            state=ConversationState.COLLECTING_COUNTRY,
        )

    async def test_client_creation_alternative_name_flow(
        self,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
    ):
        """
        Test flow where user provides alternative client name instead of confirming.
        """
        # Setup: User has been asked to confirm 'OldTech Corp' but provides different name
        alternative_message = 'No, the client is actually ModernTech Inc'

        with (
            patch(
                'services.message_processor.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
            patch('services.ldmf_country.LDMFCountryService.list') as mock_ldmf_list,
            patch('services.client_industry.ClientIndustryDataService.list') as mock_industry_list,
            patch('services.client_service.ClientServiceDataService.list') as mock_service_list,
        ):
            # Set up mocks
            mock_get_conversation.return_value = type(
                'MockConversation', (), {'id': test_conversation_id, 'State': ConversationState.COLLECTING_CLIENT_NAME}
            )

            # Mock confirmed data with proposed client name (from previous interaction)
            confirmed_data_with_proposed = ConfirmedData(proposed_client_name='OldTech Corp')
            mock_get_confirmed_data.return_value = confirmed_data_with_proposed

            # Mock search for new client name (also no results)
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
                exact_match=False,
            )

            # Mock LDMF country service to return empty countries dict
            mock_ldmf_list.return_value = {}

            # Mock industry list to return empty list
            mock_industry_list.return_value = []

            # Mock service list to return empty list
            mock_service_list.return_value = []

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': alternative_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify response - should ask about the new client name
        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        assert 'system' in data
        assert data['system'] is None
