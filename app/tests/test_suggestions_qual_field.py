"""Tests for the new suggestions_qual field in message schemas."""

from uuid import uuid4

import pytest

from constants.engagement import StructuredSuggestedPromptBody, StructuredSuggestedPromptTitle
from constants.message import MessageRole, MessageType, SystemReplyType
from core import json as core_json
from schemas import (
    MessageContent,
    MessageValidator,
    StructuredSuggestedPrompt,
    StructuredSuggestedPromptType,
    SystemMessageSerializer,
)


class TestStructuredSuggestedPrompt:
    """Test the StructuredSuggestedPrompt schema."""

    def test_create_with_all_fields(self):
        """Test creating StructuredSuggestedPrompt with all fields."""
        prompt = StructuredSuggestedPrompt(
            type=StructuredSuggestedPromptType.BUSINESS_ISSUES,
            title=StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES,
            body=StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES,
        )

        assert prompt.type == StructuredSuggestedPromptType.BUSINESS_ISSUES
        assert prompt.title == StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES
        assert prompt.body == StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES

    def test_create_with_partial_fields(self):
        """Test creating StructuredSuggestedPrompt with only some fields."""
        prompt = StructuredSuggestedPrompt(title=StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES)

        assert prompt.type is None
        assert prompt.title == StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES
        assert prompt.body is None

    def test_create_empty(self):
        """Test creating StructuredSuggestedPrompt with no fields."""
        prompt = StructuredSuggestedPrompt()

        assert prompt.type is None
        assert prompt.title is None
        assert prompt.body is None

    def test_model_dump(self):
        """Test serialization to dict."""
        prompt = StructuredSuggestedPrompt(
            type=StructuredSuggestedPromptType.BUSINESS_ISSUES,
            title=StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES,
            body=StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES,
        )

        expected = {
            'type': StructuredSuggestedPromptType.BUSINESS_ISSUES.value,
            'title': StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES.value,
            'body': StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES.value,
        }

        assert prompt.model_dump() == expected


class TestMessageValidatorSuggestionsQual:
    """Test the suggestions_qual field in MessageValidator."""

    def test_create_with_suggestions_qual_list(self):
        """Test creating MessageValidator with suggestions_qual as list of objects."""
        suggestions = [
            StructuredSuggestedPrompt(
                type=StructuredSuggestedPromptType.BUSINESS_ISSUES,
                title=StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES,
                body=StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES,
            ),
            StructuredSuggestedPrompt(
                type=StructuredSuggestedPromptType.SCOPE_AND_APPROACH,
                title=StructuredSuggestedPromptTitle.EXPAND_SCOPE_AND_APPROACH,
                body=StructuredSuggestedPromptBody.EXPAND_SCOPE_AND_APPROACH,
            ),
        ]

        message = MessageValidator(
            conversation_id=uuid4(),
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content='Test message',
            suggestions_qual=suggestions,
            system_reply_type=SystemReplyType.WELCOME_MESSAGE,
        )

        assert len(message.suggestions_qual) == 2
        assert message.suggestions_qual[0].type == StructuredSuggestedPromptType.BUSINESS_ISSUES
        assert message.suggestions_qual[1].type == StructuredSuggestedPromptType.SCOPE_AND_APPROACH

    def test_create_with_empty_suggestions_qual(self):
        """Test creating MessageValidator with empty suggestions_qual."""
        message = MessageValidator(
            conversation_id=uuid4(),
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content='Test message',
            system_reply_type=SystemReplyType.WELCOME_MESSAGE,
        )

        assert message.suggestions_qual == []

    def test_validate_suggestions_qual_from_json_string(self):
        """Test validation of suggestions_qual from JSON string."""
        json_data = [
            {
                'type': StructuredSuggestedPromptType.BUSINESS_ISSUES.value,
                'title': StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES.value,
                'body': StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES.value,
            },
            {
                'type': StructuredSuggestedPromptType.SCOPE_AND_APPROACH.value,
                'title': StructuredSuggestedPromptTitle.EXPAND_SCOPE_AND_APPROACH.value,
                'body': StructuredSuggestedPromptBody.EXPAND_SCOPE_AND_APPROACH.value,
            },
        ]
        json_string = core_json.dumps(json_data)

        # Test the validator directly
        result = MessageValidator.validate_suggestions_qual(json_string)

        assert len(result) == 2
        assert isinstance(result[0], StructuredSuggestedPrompt)
        assert result[0].type == StructuredSuggestedPromptType.BUSINESS_ISSUES
        assert result[1].type == StructuredSuggestedPromptType.SCOPE_AND_APPROACH

    def test_validate_suggestions_qual_from_dict_list(self):
        """Test validation of suggestions_qual from list of dicts."""
        dict_data = [
            {
                'type': StructuredSuggestedPromptType.BUSINESS_ISSUES.value,
                'title': StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES.value,
                'body': StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES.value,
            },
            {
                'type': StructuredSuggestedPromptType.SCOPE_AND_APPROACH.value,
                'title': StructuredSuggestedPromptTitle.EXPAND_SCOPE_AND_APPROACH.value,
            },
        ]

        result = MessageValidator.validate_suggestions_qual(dict_data)

        assert len(result) == 2
        assert isinstance(result[0], StructuredSuggestedPrompt)
        assert result[0].type == StructuredSuggestedPromptType.BUSINESS_ISSUES
        assert result[1].body is None  # Missing body should be None

    def test_validate_suggestions_qual_empty_values(self):
        """Test validation with empty/None values."""
        assert MessageValidator.validate_suggestions_qual(None) == []
        assert MessageValidator.validate_suggestions_qual([]) == []
        assert MessageValidator.validate_suggestions_qual('') == []

    def test_validate_suggestions_qual_invalid_json(self):
        """Test validation with invalid JSON string."""
        with pytest.raises(ValueError, match='Invalid JSON in suggestions_qual'):
            MessageValidator.validate_suggestions_qual('invalid json')

    def test_validate_suggestions_qual_invalid_type(self):
        """Test validation with invalid input type."""
        with pytest.raises(ValueError, match='Invalid type for suggestions_qual'):
            MessageValidator.validate_suggestions_qual(123)  # type: ignore

    def test_validate_suggestions_qual_invalid_item_type(self):
        """Test validation with invalid item types in list."""
        with pytest.raises(
            ValueError, match='Each item in suggestions_qual must be a dict or StructuredSuggestedPrompt'
        ):
            MessageValidator.validate_suggestions_qual(['invalid', 'items'])

    def test_model_dump_for_db_with_suggestions_qual(self):
        """Test database serialization with suggestions_qual field."""
        suggestions = [
            StructuredSuggestedPrompt(
                type=StructuredSuggestedPromptType.BUSINESS_ISSUES,
                title=StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES,
                body=StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES,
            ),
            StructuredSuggestedPrompt(
                type=StructuredSuggestedPromptType.SCOPE_AND_APPROACH,
                title=StructuredSuggestedPromptTitle.EXPAND_SCOPE_AND_APPROACH,
            ),
        ]

        message = MessageValidator(
            conversation_id=uuid4(),
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content='Test message',
            suggestions_qual=suggestions,
            system_reply_type=SystemReplyType.WELCOME_MESSAGE,
        )

        db_data = message.model_dump_for_db()

        # Check that SuggestionsQual is JSON serialized
        assert 'SuggestionsQual' in db_data
        suggestions_qual_json = db_data['SuggestionsQual']

        # Parse back to verify structure
        parsed_suggestions = core_json.loads(suggestions_qual_json)
        assert len(parsed_suggestions) == 2
        assert parsed_suggestions[0]['type'] == StructuredSuggestedPromptType.BUSINESS_ISSUES.value
        assert parsed_suggestions[0]['title'] == StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES.value
        assert parsed_suggestions[1]['body'] is None

    def test_model_dump_for_db_empty_suggestions_qual(self):
        """Test database serialization with empty suggestions_qual."""
        message = MessageValidator(
            conversation_id=uuid4(),
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content='Test message',
            system_reply_type=SystemReplyType.WELCOME_MESSAGE,
        )

        db_data = message.model_dump_for_db()

        # Should have empty JSON array
        assert db_data['SuggestionsQual'] == '[]'


class TestSystemMessageSerializerSuggestionsQual:
    """Test the suggestions_qual field in SystemMessageSerializer."""

    def test_validate_suggestions_qual_from_db(self):
        """Test deserializing suggestions_qual from database JSON."""
        # Simulate data coming from database
        db_data = {
            'PublicId': uuid4(),
            'ConversationPublicId': uuid4(),
            'Role': MessageRole.SYSTEM,
            'Type': MessageType.TEXT,
            'Content': 'Test message',
            'CreatedAt': '2024-01-01T00:00:00',
            'Options': '[]',
            'SuggestedPrompts': '[]',
            'SuggestionsQual': core_json.dumps(
                [
                    {
                        'type': StructuredSuggestedPromptType.BUSINESS_ISSUES.value,
                        'title': StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES.value,
                        'body': StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES.value,
                    }
                ]
            ),
            'SystemReplyType': SystemReplyType.WELCOME_MESSAGE,
        }

        message = SystemMessageSerializer.model_validate(db_data)

        assert len(message.suggestions_qual) == 1
        assert isinstance(message.suggestions_qual[0], StructuredSuggestedPrompt)
        assert message.suggestions_qual[0].type == StructuredSuggestedPromptType.BUSINESS_ISSUES
        assert message.suggestions_qual[0].title == StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES


class TestMessageContentSuggestionsQual:
    """Test the suggestions_qual field in MessageContent."""

    def test_message_content_with_suggestions_qual(self):
        """Test MessageContent with suggestions_qual field."""
        suggestions = [
            StructuredSuggestedPrompt(
                type=StructuredSuggestedPromptType.BUSINESS_ISSUES,
                title=StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES,
                body=StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES,
            )
        ]

        content = MessageContent(
            formatted_content='Test content',
            reply_type=SystemReplyType.WELCOME_MESSAGE,
            extracted_options=[],
            suggested_prompts=[],
            suggestions_qual=suggestions,
        )

        assert len(content.suggestions_qual) == 1
        assert content.suggestions_qual[0].type == StructuredSuggestedPromptType.BUSINESS_ISSUES

    def test_message_content_to_message_validator(self):
        """Test converting MessageContent to MessageValidator with suggestions_qual."""
        from datetime import datetime

        from schemas.conversation_message.message import BaseMessageSerializer

        suggestions = [
            StructuredSuggestedPrompt(
                type=StructuredSuggestedPromptType.BUSINESS_ISSUES,
                title=StructuredSuggestedPromptTitle.EXPAND_BUSINESS_ISSUES,
                body=StructuredSuggestedPromptBody.EXPAND_BUSINESS_ISSUES,
            )
        ]

        content = MessageContent(
            formatted_content='Test content',
            reply_type=SystemReplyType.WELCOME_MESSAGE,
            extracted_options=[],
            suggested_prompts=[],
            suggestions_qual=suggestions,
        )

        # Create a mock last message
        last_message = BaseMessageSerializer(
            id=uuid4(),
            conversation_id=uuid4(),
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content='User message',
            created_at=datetime.now(),
        )

        conversation_id = uuid4()
        validator = content.to_message_validator(conversation_id, last_message)

        assert len(validator.suggestions_qual) == 1
        assert validator.suggestions_qual[0].type == StructuredSuggestedPromptType.BUSINESS_ISSUES
        assert validator.conversation_id == conversation_id
