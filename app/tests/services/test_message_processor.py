from datetime import datetime
from unittest.mock import AsyncMock, patch
from uuid import UUID

import pytest

from constants.message import MessageR<PERSON>, MessageType, SystemReplyType
from schemas.confirmed_data import ConfirmedData
from schemas.context_entities import ConversationData
from schemas.conversation import ConversationResponse
from schemas.conversation_message.message import UserMessageSerializer
from schemas.extracted_data import AggregatedData
from services.message_processor import (
    MAX_CLIENT_NAME_LENGTH,
    ConversationMessageProcessor,
)


class TestMessageProcessor:
    """Unit tests for ConversationMessageProcessor."""

    @pytest.fixture
    def mock_user_message(self) -> UserMessageSerializer:
        """Fixture for a mock UserMessageSerializer."""
        return UserMessageSerializer(
            id=UUID('a4651e95-8124-441f-a32e-41de17e78b90'),
            conversation_id=UUID('a4651e95-8124-441f-a32e-41de17e78b90'),
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content='Test message',
            created_at=datetime.now(),
            selected_option=None,
        )

    @pytest.fixture
    def mock_conversation_response(self) -> ConversationResponse:
        """Fixture for a mock ConversationResponse."""
        return ConversationResponse(
            id=UUID('a4651e95-8124-441f-a32e-41de17e78b90'),
            qual_id=None,
            dash_activity_id=None,
            is_completed=False,
            created_at=datetime.now(),
            created_by_id=UUID('a4651e95-8124-441f-a32e-41de17e78b90'),
            created_by_name='test_user',
        )

    @pytest.fixture
    def mock_conversation_data(self, mock_conversation_response: ConversationResponse) -> ConversationData:
        """Fixture for a mock ConversationData."""
        return ConversationData(
            conversation=mock_conversation_response,
            confirmed_data=ConfirmedData(),
            aggregated_data=AggregatedData(),
            conversation_message_history=[],
        )

    @pytest.mark.asyncio
    async def test_handle_client_name_input_too_long(
        self,
        mock_conversation_data: ConversationData,
        mock_user_message: UserMessageSerializer,
    ):
        """Test that client name exceeding max length is handled correctly."""
        processor = ConversationMessageProcessor(
            conversation_id=mock_conversation_data.conversation.id,
            user_message=mock_user_message,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            conversation_repository=AsyncMock(),
            conversation_message_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
            conversation_message_history=[],
            conversation_data=mock_conversation_data,
        )

        long_client_name = 'a' * (MAX_CLIENT_NAME_LENGTH + 1)
        result = await processor._handle_client_name_input(long_client_name)

        assert result.system_reply_type == SystemReplyType.CLIENT_NAME_TOO_LONG

    @pytest.mark.asyncio
    async def test_handle_client_name_input_valid(
        self,
        mock_conversation_data: ConversationData,
        mock_user_message: UserMessageSerializer,
    ):
        """Test that a valid client name is handled correctly."""
        processor = ConversationMessageProcessor(
            conversation_id=mock_conversation_data.conversation.id,
            user_message=mock_user_message,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            conversation_repository=AsyncMock(),
            conversation_message_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
            conversation_message_history=[],
            conversation_data=mock_conversation_data,
        )

        client_name = 'Valid Client Name'
        with patch.object(processor.extracted_data_service, 'quals_clients_repository') as mock_repo:
            mock_repo.search_clients.return_value = AsyncMock(clients=[], exact_match=False)
            result = await processor._handle_client_name_input(client_name)

        assert result.system_reply_type != SystemReplyType.CLIENT_NAME_TOO_LONG
