from functools import cached_property
import mimetypes
from typing import Sequence

from pydantic import HttpUrl, field_validator

from constants.environment import Environment
from core.schemas import CustomModel


__all__ = [
    'ADSettings',
    'SignalRSettings',
    'AuthSettings',
    'DatabaseSettings',
    'BlobStorageSettings',
    'KXDashAPISettings',
    'QualsClientsAPISettings',
    'IndustriesAPISettings',
    'ServicesAPISettings',
    'RolesAPISettings',
    'UserInfoAPISettings',
    'LDMFCountriesAPISettings',
    'HTTPClientBackoffSettings',
    'HTTPClientSettings',
    'QueueSettings',
    'OpenAISettings',
    'CacheSettings',
    'Settings',
]


class ADSettings(CustomModel):
    aad_instance: str
    api_audience: str
    aad_tenant_id: str
    required_scopes: frozenset
    blocked_domains: Sequence[str]


class SignalRSettings(CustomModel):
    connection_string: str
    token_lifespan: int
    hub_name: str


class AuthSettings(CustomModel):
    ad: ADSettings
    signal_r: SignalRSettings
    auth_free_endpoints: frozenset[str]


class DatabaseSettings(CustomModel):
    host: str
    port: str
    user: str
    password: str
    name: str
    driver: str

    @cached_property
    def uri(self) -> str:
        return (
            f'mssql+aioodbc:///?odbc_connect=DRIVER={{{self.driver}}};SERVER={self.host},{self.port};'
            f'DATABASE={self.name};UID={self.user};PWD={self.password};MARS_Connection=Yes;'
        )


class BlobStorageSettings(CustomModel):
    """Settings for Azure Blob Storage."""

    connection_string: str
    default_container_name: str
    # Document container settings
    document_container_name: str
    max_file_size: int
    max_docs_per_conversation: int
    max_conversation_size: int
    supported_file_formats: set[str]

    @cached_property
    def supported_extensions(self) -> set[str]:
        return {ext[1:] for mime_type in self.supported_file_formats if (ext := mimetypes.guess_extension(mime_type))}


class KXDashAPISettings(CustomModel):
    base_url: HttpUrl


class QualsClientsAPISettings(CustomModel):
    base_url: HttpUrl
    mock_client_api_enabled: bool
    mock_client_get_references_list_enabled: bool


class IndustriesAPISettings(CustomModel):
    base_url: HttpUrl


class ServicesAPISettings(CustomModel):
    base_url: HttpUrl


class RolesAPISettings(CustomModel):
    base_url: HttpUrl


class UserInfoAPISettings(CustomModel):
    base_url: HttpUrl


class LDMFCountriesAPISettings(CustomModel):
    base_url: HttpUrl


class HTTPClientBackoffSettings(CustomModel):
    max_retries: int
    backoff_base: int
    backoff_factor: float


class HTTPClientSettings(CustomModel):
    timeout: int
    follow_redirects: bool
    verify_ssl: bool
    max_connections: int
    max_keepalive_connections: int
    backoff: HTTPClientBackoffSettings

    @field_validator('timeout', 'max_connections', 'max_keepalive_connections')
    @classmethod
    def validate_positive_integers(cls, v):
        if v <= 0:
            raise ValueError('Value must be positive')
        return v

    @field_validator('max_keepalive_connections')
    @classmethod
    def validate_keepalive_connections(cls, v, info):
        if 'max_connections' in info.data and v > info.data['max_connections']:
            raise ValueError('max_keepalive_connections must not exceed max_connections')
        return v


class QueueSettings(CustomModel):
    connection_string: str
    content_queue_name: str


class OpenAISettings(CustomModel):
    """Settings for Azure OpenAI API."""

    endpoint: str
    key: str
    deployment: str
    model: str
    api_version: str
    default_temperature: float
    max_completion_tokens: int


class CacheSettings(CustomModel):
    """Settings for cache configuration."""

    default_maxsize: int
    default_ttl: int
    enable_serialization: bool = True

    # Specific cache configurations
    ldmf_countries_maxsize: int
    ldmf_countries_ttl: int

    quals_clients_maxsize: int
    quals_clients_ttl: int


class Settings(CustomModel):
    project_name: str
    version: str
    environment: Environment
    log_level: int
    debug: bool
    allowed_hosts: Sequence[str]
    auth: AuthSettings
    db: DatabaseSettings
    blob_storage: BlobStorageSettings
    http_client: HTTPClientSettings
    kx_dash_api: KXDashAPISettings
    quals_clients_api: QualsClientsAPISettings
    document_queue: QueueSettings
    openai: OpenAISettings
    cache: CacheSettings
    industries_api: IndustriesAPISettings
    services_api: ServicesAPISettings
    roles_api: RolesAPISettings
    user_info_api: UserInfoAPISettings
    ldmf_countries_api: LDMFCountriesAPISettings
    support_url: HttpUrl

    # Debugging & logging
    append_collected_data_to_message_response: bool
