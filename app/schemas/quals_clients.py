from pydantic import Field

from core.schemas import CustomModel


__all__ = [
    'ClientSearchRequest',
    'ClientSearchResponse',
    'ClientCreateRequest',
    'ClientCreateResponse',
    'ClientSearchItem',
    'ClientComprehensive',
    'ClientAPIParamsItem',
    'ClientConfidentialityItem',
    'ClientSummaryItem',
]


class ClientSearchRequest(CustomModel):
    """Schema for client search request parameters."""

    contains: str = Field(..., description='Text query to search for in client names')
    page_size: int = Field(default=20, description='Number of results per page')
    page_idx: int = Field(default=0, description='Page index starting from 0')


class ClientSearchItem(CustomModel):
    """Schema for individual client item in search results."""

    id: int = Field(..., description='Unique client identifier')
    name: str = Field(..., description='Client name')
    quals_count: int = Field(..., alias='qualsCount', description='Number of quals for this client')
    client_confidentiality: int = Field(..., alias='clientConfidentiality', description='Client confidentiality level')


class ClientSearchResponse(CustomModel):
    """Schema for client search response."""

    clients: list[ClientSearchItem] = Field(..., description='List of matching clients')
    total_count: int = Field(..., description='Total number of matching clients')
    page_size: int = Field(..., description='Number of results per page')
    page_idx: int = Field(..., description='Current page index')
    exact_match: bool = Field(..., description='Whether the search query was an exact match')


class ClientCreateRequest(CustomModel):
    """Schema for creating a new client."""

    name: str = Field(..., description='Client name')


class ClientComprehensive(CustomModel):
    """Schema for comprehensive client object returned from create operation."""

    id: int = Field(..., description='Unique client identifier')
    name: str = Field(..., description='Client name')
    description: str | None = Field(None, description='Client description')
    primary_local_industry: str | None = Field(None, alias='primaryLocalIndustry', description='Primary local industry')
    primary_global_industry: str | None = Field(
        None, alias='primaryGlobalIndustry', description='Primary global industry'
    )
    secondary_local_industries: list[str] = Field(
        default=[], alias='secondaryLocalIndustries', description='Secondary local industries'
    )
    secondary_global_industries: list[str] = Field(
        default=[], alias='secondaryGlobalIndustries', description='Secondary global industries'
    )


class ClientCreateResponse(CustomModel):
    """Schema for client creation response."""

    client: ClientComprehensive = Field(..., description='Created client data')
    success: bool = Field(default=True, description='Whether the operation was successful')
    message: str | None = Field(None, description='Optional message about the operation')


class ClientAPIParamsItem(CustomModel):
    """Schema for client name internal usage."""

    sort_order: int | None = None
    catalogue_number: int | None = None
    catalogue_type: int | None = None
    name: str | None = None
    id: int | None = None


class ClientConfidentialityItem(CustomModel):
    """Schema for client confidentiality."""

    id: int | None = None
    name: str | None = None


class ClientSummaryItem(CustomModel):
    """Schema for client summary item in search results."""

    id: int = Field(..., description='Unique client identifier')
    name: str = Field(..., description='Client name')
    quals_count: int = Field(..., alias='qualsCount', description='Number of quals for this client')
    client_confidentiality: str = Field(..., alias='clientConfidentiality', description='Client confidentiality level')
