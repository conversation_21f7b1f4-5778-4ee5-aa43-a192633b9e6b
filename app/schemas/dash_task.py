from datetime import date
from typing import List

from pydantic import ConfigDict, EmailStr, Field

from core.schemas import CustomModel
from schemas.extracted_data import IndustryData


__all__ = ['DashTaskRequest', 'DashTaskResponse']


class DashTaskRequest(CustomModel):
    """Schema for creating a new Dash task."""

    activity_id: int | None = None
    activity_name: str | None = None
    activity_type: str | None = None
    client_name: str | None = None
    member_firm: str | None = None
    country: str | None = None
    global_business: str | None = None
    global_business_service_area: str | None = None
    global_business_service_line: str | None = None
    global_industry: str | None = None
    global_industry_sector: str | None = None
    engagement_code: str | None = None
    global_lcsp_emails: List[EmailStr] | None = None
    engagement_lep_emails: List[EmailStr] | None = None
    engagement_manager_emails: List[EmailStr] | None = None
    activity_owner_emails: List[EmailStr] | None = None
    engagement_start_date: date | None = None
    engagement_end_date: date | None = None
    due_date: date | None = None
    status: str | None = None


class DashTaskResponse(CustomModel):
    """Schema for Dash task response."""

    activity_id: int = Field(validation_alias='activityId')
    activity_name: str | None = Field(default=None, validation_alias='activityName')
    activity_type: str | None = Field(default=None, validation_alias='activityType')
    client_name: str | None = Field(default=None, validation_alias='clientName')
    member_firm: str | None = Field(default=None, validation_alias='memberFirm')
    country: list[str] | None = Field(default=None, validation_alias='country')
    global_business: str | None = Field(default=None, validation_alias='globalBusiness')
    global_business_service_area: str | None = Field(default=None, validation_alias='globalBusinessServiceArea')
    global_business_service_line: str | None = Field(default=None, validation_alias='globalBusinessServiceLine')
    global_industry: str | None = Field(default=None, validation_alias='globalIndustry')
    global_industry_sector: str | None = Field(default=None, validation_alias='globalIndustrySector')
    client_industries: List[IndustryData] | None = Field(default=None, validation_alias='clientIndustries')
    engagement_code: str | None = Field(default=None, validation_alias='engagementCode')
    global_lcsp_emails: List[EmailStr] | None = Field(default=None, validation_alias='globalLCSPEmails')
    engagement_lep_emails: List[EmailStr] | None = Field(default=None, validation_alias='engagementLepEmails')
    engagement_manager_emails: List[EmailStr] | None = Field(default=None, validation_alias='engagementManagerEmails')
    activity_owner_emails: List[EmailStr] | None = Field(default=None, validation_alias='activityOwnerEmails')
    engagement_start_date: date | None = Field(default=None, validation_alias='engagementStartDate')
    engagement_end_date: date | None = Field(default=None, validation_alias='engagementEndDate')
    engagement_start_date_original: str | None = Field(default=None, validation_alias='engagementStartDateOriginal')
    engagement_end_date_original: str | None = Field(default=None, validation_alias='engagementEndDateOriginal')
    due_date: date | None = Field(default=None, validation_alias='dueDate')
    status: str | None = Field(default=None, validation_alias='status')

    model_config = ConfigDict(
        from_attributes=True,
    )
