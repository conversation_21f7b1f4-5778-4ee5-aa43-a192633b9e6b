from typing import List

from pydantic import Field

from core.schemas import CustomModel
from schemas.project_roles import ProjectRolesData


__all__ = [
    'TeamMemberGraphItem',
    'TeamMember',
]


class TeamMemberGraphItem(CustomModel):
    """Basic user details returned from team-members-graph endpoint."""

    id: int | None = None
    user_id: int | None = Field(default=None, alias='userId')
    location: str | None = None
    level: str | None = None
    first_name: str | None = Field(default=None, alias='firstName')
    last_name: str | None = Field(default=None, alias='lastName')
    second_name: str | None = Field(default=None, alias='secondName')
    deloitte_id: str | None = Field(default=None, alias='deloitteId')
    office_country: str | None = Field(default=None, alias='officeCountry')
    office_city: str | None = Field(default=None, alias='officeCity')


class TeamMember(CustomModel):
    """Enhanced team member data used in CombinedExtractedDataResponse.team_roles."""

    # Existing fields extracted from documents/prompts
    name: str | None = None
    email: str | None = None
    is_approver: bool | None = None
    is_contact: bool | None = None
    duration: int | None = None
    roles: List[ProjectRolesData] = Field(default_factory=list)

    # Enriched fields from external APIs
    id: int | None = None
    user_id: int | None = Field(default=None, alias='userId')
    location: str | None = None
    level: str | None = None
    first_name: str | None = Field(default=None, alias='firstName')
    last_name: str | None = Field(default=None, alias='lastName')
    second_name: str | None = Field(default=None, alias='secondName')
    deloitte_id: str | None = Field(default=None, alias='deloitteId')
    office_country: str | None = Field(default=None, alias='officeCountry')
    office_city: str | None = Field(default=None, alias='officeCity')
    avatar: str | None = None

